# AI婚纱照生成产品技术实施指南

## 📋 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-03
- **适用范围**: MVP版本开发
- **技术栈**: Next.js 15 + React 19 + TypeScript

## 🎯 1. MVP版本核心功能

### 1.1 功能优先级
1. **P0 (必须)**: 游客模式、预览生成、快速支付
2. **P1 (重要)**: 用户引导、数据分析、安全防护
3. **P2 (可选)**: 社交分享、高级功能、个性化

### 1.2 技术架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Next.js) │    │   API Routes    │    │   AI Services   │
│                 │    │                 │    │                 │
│ • 游客模式组件   │◄──►│ • 会话管理      │◄──►│ • OpenAI DALL-E │
│ • 步骤引导      │    │ • 预览生成      │    │ • HenAPI        │
│ • 快速支付      │    │ • 支付处理      │    │ • APICore       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Local Storage  │    │   PostgreSQL    │    │     Stripe      │
│                 │    │                 │    │                 │
│ • 游客会话      │    │ • 用户数据      │    │ • 支付处理      │
│ • 临时状态      │    │ • 订单记录      │    │ • 订阅管理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 2. 核心功能实现

### 2.1 游客会话管理系统

#### 2.1.1 会话管理类
```typescript
// src/lib/guest-session-manager.ts
import { v4 as uuidv4 } from 'uuid';

export interface GuestSession {
  sessionId: string;
  uploadedImages: string[];
  generatedPreviews: number;
  freeCreditsUsed: number;
  maxFreeCredits: number;
  createdAt: Date;
  expiresAt: Date;
  ipAddress?: string;
  userAgent?: string;
  lastActivity: Date;
}

export class GuestSessionManager {
  private static instance: GuestSessionManager;
  private sessions: Map<string, GuestSession> = new Map();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // 每小时清理过期会话
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredSessions();
    }, 60 * 60 * 1000);
  }

  static getInstance(): GuestSessionManager {
    if (!GuestSessionManager.instance) {
      GuestSessionManager.instance = new GuestSessionManager();
    }
    return GuestSessionManager.instance;
  }

  createSession(ipAddress?: string, userAgent?: string): string {
    const sessionId = uuidv4();
    const now = new Date();
    
    const session: GuestSession = {
      sessionId,
      uploadedImages: [],
      generatedPreviews: 0,
      freeCreditsUsed: 0,
      maxFreeCredits: 1,
      createdAt: now,
      expiresAt: new Date(now.getTime() + 24 * 60 * 60 * 1000), // 24小时
      ipAddress,
      userAgent,
      lastActivity: now
    };
    
    this.sessions.set(sessionId, session);
    return sessionId;
  }

  getSession(sessionId: string): GuestSession | null {
    const session = this.sessions.get(sessionId);
    if (session && session.expiresAt > new Date()) {
      // 更新最后活动时间
      session.lastActivity = new Date();
      return session;
    }
    return null;
  }

  canGeneratePreview(sessionId: string): boolean {
    const session = this.getSession(sessionId);
    return session ? session.freeCreditsUsed < session.maxFreeCredits : false;
  }

  consumeFreeCredit(sessionId: string): boolean {
    const session = this.getSession(sessionId);
    if (session && this.canGeneratePreview(sessionId)) {
      session.freeCreditsUsed++;
      session.generatedPreviews++;
      session.lastActivity = new Date();
      return true;
    }
    return false;
  }

  addUploadedImage(sessionId: string, imageUrl: string): boolean {
    const session = this.getSession(sessionId);
    if (session) {
      session.uploadedImages.push(imageUrl);
      session.lastActivity = new Date();
      return true;
    }
    return false;
  }

  getRemainingCredits(sessionId: string): number {
    const session = this.getSession(sessionId);
    return session ? session.maxFreeCredits - session.freeCreditsUsed : 0;
  }

  private cleanupExpiredSessions(): void {
    const now = new Date();
    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.expiresAt <= now) {
        this.sessions.delete(sessionId);
      }
    }
  }

  // 获取会话统计信息
  getSessionStats(): {
    totalSessions: number;
    activeSessions: number;
    totalPreviews: number;
  } {
    const now = new Date();
    let activeSessions = 0;
    let totalPreviews = 0;

    for (const session of this.sessions.values()) {
      if (session.expiresAt > now) {
        activeSessions++;
      }
      totalPreviews += session.generatedPreviews;
    }

    return {
      totalSessions: this.sessions.size,
      activeSessions,
      totalPreviews
    };
  }
}

// 导出单例实例
export const guestSessionManager = GuestSessionManager.getInstance();
```

#### 2.1.2 API路由实现
```typescript
// src/app/api/guest/session/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { guestSessionManager } from '@/lib/guest-session-manager';
import { rateLimit } from '@/lib/rate-limit';

// 速率限制：每IP每分钟最多5次请求
const limiter = rateLimit({
  interval: 60 * 1000, // 1分钟
  uniqueTokenPerInterval: 500, // 支持500个不同IP
});

export async function POST(request: NextRequest) {
  try {
    // 应用速率限制
    const identifier = request.ip || 'anonymous';
    const { success } = await limiter.check(5, identifier);
    
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      );
    }

    const clientIP = request.ip || 
                    request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    const sessionId = guestSessionManager.createSession(clientIP, userAgent);
    
    return NextResponse.json({
      success: true,
      sessionId,
      maxFreeCredits: 1,
      expiresIn: 24 * 60 * 60 // 24小时（秒）
    });
  } catch (error) {
    console.error('Error creating guest session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create session' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const sessionId = request.nextUrl.searchParams.get('sessionId');
    
    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID required' },
        { status: 400 }
      );
    }
    
    const session = guestSessionManager.getSession(sessionId);
    
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Session not found or expired' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      canGenerate: guestSessionManager.canGeneratePreview(sessionId),
      freeCreditsUsed: session.freeCreditsUsed,
      maxFreeCredits: session.maxFreeCredits,
      remainingCredits: guestSessionManager.getRemainingCredits(sessionId),
      expiresAt: session.expiresAt.toISOString()
    });
  } catch (error) {
    console.error('Error checking guest session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check session' },
      { status: 500 }
    );
  }
}
```

### 2.2 预览图生成系统

#### 2.2.1 预览配置
```typescript
// src/lib/preview-config.ts
export interface ImageConfig {
  resolution: { width: number; height: number };
  watermark: {
    enabled: boolean;
    text?: string;
    position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
    opacity?: number;
    fontSize?: number;
    color?: string;
  };
  quality: number;
  format: 'jpeg' | 'png' | 'webp';
}

export const PREVIEW_CONFIG: ImageConfig = {
  resolution: { width: 1280, height: 720 }, // 720p
  watermark: {
    enabled: true,
    text: "Hera AI Preview",
    position: "bottom-right",
    opacity: 0.7,
    fontSize: 24,
    color: "#ffffff"
  },
  quality: 0.7,
  format: 'jpeg'
};

export const PREMIUM_CONFIG: ImageConfig = {
  resolution: { width: 3840, height: 2160 }, // 4K
  watermark: { enabled: false },
  quality: 0.95,
  format: 'png'
};

export const STYLE_PROMPTS = {
  'chinese-traditional': {
    prompt: 'A beautiful bride in traditional Chinese wedding dress (qipao or xiuhe), red and gold colors, elegant pose facing camera, ornate traditional Chinese architecture background, professional wedding photography, high quality, detailed',
    negativePrompt: 'blurry, low quality, distorted face, side view, back view'
  },
  'western-elegant': {
    prompt: 'A beautiful bride in elegant white wedding dress, classic Western style, graceful pose facing camera, church or garden background, professional wedding photography, high quality, detailed',
    negativePrompt: 'blurry, low quality, distorted face, side view, back view'
  },
  'beach-sunset': {
    prompt: 'A beautiful bride in flowing beach wedding dress, golden sunset background, ocean waves, romantic atmosphere, facing camera, professional wedding photography, high quality, detailed',
    negativePrompt: 'blurry, low quality, distorted face, side view, back view'
  },
  'forest-romantic': {
    prompt: 'A beautiful bride in romantic wedding dress, enchanted forest background, natural lighting, fairy-tale atmosphere, facing camera, professional wedding photography, high quality, detailed',
    negativePrompt: 'blurry, low quality, distorted face, side view, back view'
  },
  'vintage-classic': {
    prompt: 'A beautiful bride in vintage wedding dress, classic retro style, timeless elegance, vintage background, facing camera, professional wedding photography, high quality, detailed',
    negativePrompt: 'blurry, low quality, distorted face, side view, back view'
  },
  'modern-chic': {
    prompt: 'A beautiful bride in modern minimalist wedding dress, contemporary style, clean lines, modern background, facing camera, professional wedding photography, high quality, detailed',
    negativePrompt: 'blurry, low quality, distorted face, side view, back view'
  }
};
```

#### 2.2.2 AI图像生成服务
```typescript
// src/lib/ai-generation.ts
import OpenAI from 'openai';
import { PREVIEW_CONFIG, PREMIUM_CONFIG, STYLE_PROMPTS, ImageConfig } from './preview-config';
import { addWatermark } from './image-utils';

interface GenerationRequest {
  imageData: string; // base64 encoded image
  style: keyof typeof STYLE_PROMPTS;
  config: ImageConfig;
  userId?: string;
  sessionId?: string;
}

interface GenerationResult {
  url: string;
  width: number;
  height: number;
  hasWatermark: boolean;
  generationTime: number;
}

export class AIGenerationService {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1'
    });
  }

  async generateWeddingPhoto(request: GenerationRequest): Promise<GenerationResult> {
    const startTime = Date.now();

    try {
      const styleConfig = STYLE_PROMPTS[request.style];
      if (!styleConfig) {
        throw new Error(`Unsupported style: ${request.style}`);
      }

      // 构建完整的prompt
      const fullPrompt = `${styleConfig.prompt}, replace the person's face with the uploaded face while maintaining the pose and style`;

      console.log(`🎨 Generating ${request.config === PREVIEW_CONFIG ? 'preview' : 'premium'} image for style: ${request.style}`);

      // 调用OpenAI DALL-E API
      const response = await this.openai.images.generate({
        model: "dall-e-3",
        prompt: fullPrompt,
        n: 1,
        size: this.mapResolutionToSize(request.config.resolution),
        quality: request.config.quality > 0.8 ? "hd" : "standard",
        response_format: "url"
      });

      if (!response.data || response.data.length === 0) {
        throw new Error('No image generated');
      }

      let imageUrl = response.data[0].url!;

      // 如果需要添加水印
      if (request.config.watermark.enabled) {
        imageUrl = await addWatermark(imageUrl, request.config.watermark);
      }

      const generationTime = Date.now() - startTime;

      console.log(`✅ Image generated successfully in ${generationTime}ms`);

      return {
        url: imageUrl,
        width: request.config.resolution.width,
        height: request.config.resolution.height,
        hasWatermark: request.config.watermark.enabled,
        generationTime
      };

    } catch (error) {
      console.error('❌ AI generation failed:', error);
      throw new Error(`Generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private mapResolutionToSize(resolution: { width: number; height: number }): "1024x1024" | "1792x1024" | "1024x1792" {
    // DALL-E 3 支持的尺寸
    if (resolution.width === resolution.height) {
      return "1024x1024";
    } else if (resolution.width > resolution.height) {
      return "1792x1024";
    } else {
      return "1024x1792";
    }
  }
}

// 导出单例实例
export const aiGenerationService = new AIGenerationService();

// 便捷函数
export async function generatePreviewImage(request: Omit<GenerationRequest, 'config'>): Promise<GenerationResult> {
  return aiGenerationService.generateWeddingPhoto({
    ...request,
    config: PREVIEW_CONFIG
  });
}

export async function generatePremiumImage(request: Omit<GenerationRequest, 'config'>): Promise<GenerationResult> {
  return aiGenerationService.generateWeddingPhoto({
    ...request,
    config: PREMIUM_CONFIG
  });
}
```

#### 2.2.3 图像处理工具
```typescript
// src/lib/image-utils.ts
import sharp from 'sharp';

interface WatermarkConfig {
  text: string;
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
  opacity: number;
  fontSize: number;
  color: string;
}

export async function addWatermark(imageUrl: string, watermarkConfig: WatermarkConfig): Promise<string> {
  try {
    // 下载原图
    const response = await fetch(imageUrl);
    const imageBuffer = await response.arrayBuffer();

    // 创建水印SVG
    const watermarkSvg = createWatermarkSvg(watermarkConfig);

    // 使用Sharp添加水印
    const processedImage = await sharp(Buffer.from(imageBuffer))
      .composite([{
        input: Buffer.from(watermarkSvg),
        gravity: mapPositionToGravity(watermarkConfig.position),
        blend: 'over'
      }])
      .jpeg({ quality: 90 })
      .toBuffer();

    // 上传到云存储或返回base64
    return await uploadProcessedImage(processedImage);

  } catch (error) {
    console.error('Error adding watermark:', error);
    return imageUrl; // 失败时返回原图
  }
}

function createWatermarkSvg(config: WatermarkConfig): string {
  return `
    <svg width="300" height="50">
      <text
        x="10"
        y="35"
        font-family="Arial, sans-serif"
        font-size="${config.fontSize}"
        fill="${config.color}"
        opacity="${config.opacity}"
      >
        ${config.text}
      </text>
    </svg>
  `;
}

function mapPositionToGravity(position: string): string {
  const gravityMap: Record<string, string> = {
    'top-left': 'northwest',
    'top-right': 'northeast',
    'bottom-left': 'southwest',
    'bottom-right': 'southeast',
    'center': 'center'
  };
  return gravityMap[position] || 'southeast';
}

async function uploadProcessedImage(imageBuffer: Buffer): Promise<string> {
  // 这里可以上传到云存储服务（如AWS S3、阿里云OSS等）
  // 暂时返回base64格式
  const base64 = imageBuffer.toString('base64');
  return `data:image/jpeg;base64,${base64}`;
}

export async function compressImage(file: File, quality: number = 0.8): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算压缩后的尺寸
      const maxWidth = 1920;
      const maxHeight = 1080;
      let { width, height } = img;

      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制压缩后的图像
      ctx?.drawImage(img, 0, 0, width, height);

      canvas.toBlob((blob) => {
        if (blob) {
          const compressedFile = new File([blob], file.name, {
            type: 'image/jpeg',
            lastModified: Date.now()
          });
          resolve(compressedFile);
        } else {
          reject(new Error('Compression failed'));
        }
      }, 'image/jpeg', quality);
    };

    img.onerror = () => reject(new Error('Image load failed'));
    img.src = URL.createObjectURL(file);
  });
}
```

### 2.3 游客预览生成API
```typescript
// src/app/api/guest/generate/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { guestSessionManager } from '@/lib/guest-session-manager';
import { generatePreviewImage } from '@/lib/ai-generation';
import { rateLimit } from '@/lib/rate-limit';

// 生成限制：每IP每小时最多3次
const generateLimiter = rateLimit({
  interval: 60 * 60 * 1000, // 1小时
  uniqueTokenPerInterval: 500,
});

export async function POST(request: NextRequest) {
  try {
    const { sessionId, imageData, style } = await request.json();

    // 验证必要参数
    if (!sessionId || !imageData || !style) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // 应用生成速率限制
    const identifier = request.ip || 'anonymous';
    const { success } = await generateLimiter.check(3, identifier);

    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Generation rate limit exceeded' },
        { status: 429 }
      );
    }

    // 验证游客会话
    if (!guestSessionManager.canGeneratePreview(sessionId)) {
      return NextResponse.json(
        {
          success: false,
          error: 'No free credits available',
          remainingCredits: guestSessionManager.getRemainingCredits(sessionId)
        },
        { status: 403 }
      );
    }

    console.log(`🎨 Starting preview generation for session: ${sessionId}, style: ${style}`);

    // 生成预览图
    const result = await generatePreviewImage({
      imageData,
      style,
      sessionId
    });

    // 消费免费积分
    const creditConsumed = guestSessionManager.consumeFreeCredit(sessionId);

    if (!creditConsumed) {
      console.error('❌ Failed to consume credit after successful generation');
    }

    // 记录生成历史到数据库
    await recordPreviewGeneration({
      sessionId,
      imageUrl: result.url,
      style,
      resolution: '720p',
      hasWatermark: true,
      generationTime: result.generationTime
    });

    console.log(`✅ Preview generated successfully for session: ${sessionId}`);

    return NextResponse.json({
      success: true,
      imageUrl: result.url,
      width: result.width,
      height: result.height,
      hasWatermark: result.hasWatermark,
      remainingCredits: guestSessionManager.getRemainingCredits(sessionId),
      generationTime: result.generationTime
    });

  } catch (error) {
    console.error('❌ Preview generation failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Generation failed',
        code: 'GENERATION_ERROR'
      },
      { status: 500 }
    );
  }
}

// 记录预览生成历史
async function recordPreviewGeneration(data: {
  sessionId: string;
  imageUrl: string;
  style: string;
  resolution: string;
  hasWatermark: boolean;
  generationTime: number;
}) {
  try {
    // 这里可以记录到数据库
    console.log('📝 Recording preview generation:', {
      sessionId: data.sessionId,
      style: data.style,
      resolution: data.resolution,
      generationTime: data.generationTime
    });

    // 实际实现中可以使用Prisma保存到数据库
    // await prisma.previewGeneration.create({ data });

  } catch (error) {
    console.error('Failed to record preview generation:', error);
    // 不抛出错误，避免影响主流程
  }
}
```

### 2.4 快速结账系统

#### 2.4.1 结账API
```typescript
// src/app/api/checkout/guest/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { guestSessionManager } from '@/lib/guest-session-manager';
import { createStripeCheckoutSession } from '@/lib/stripe-utils';
import { validatePhoneNumber, validateEmail } from '@/lib/validation';

interface CheckoutRequest {
  sessionId: string;
  planId: 'single' | 'bundle' | 'basic' | 'premium';
  contactInfo: {
    phone?: string;
    email?: string;
  };
  imageData?: string;
  style?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: CheckoutRequest = await request.json();
    const { sessionId, planId, contactInfo, imageData, style } = body;

    // 验证必要参数
    if (!sessionId || !planId || !contactInfo) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // 验证联系方式（至少需要手机号或邮箱）
    if (!contactInfo.phone && !contactInfo.email) {
      return NextResponse.json(
        { success: false, error: 'Phone number or email required' },
        { status: 400 }
      );
    }

    if (contactInfo.phone && !validatePhoneNumber(contactInfo.phone)) {
      return NextResponse.json(
        { success: false, error: 'Invalid phone number' },
        { status: 400 }
      );
    }

    if (contactInfo.email && !validateEmail(contactInfo.email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email address' },
        { status: 400 }
      );
    }

    // 验证游客会话
    const session = guestSessionManager.getSession(sessionId);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired session' },
        { status: 404 }
      );
    }

    // 获取定价信息
    const pricing = getPricingForPlan(planId);
    if (!pricing) {
      return NextResponse.json(
        { success: false, error: 'Invalid plan' },
        { status: 400 }
      );
    }

    console.log(`💳 Creating checkout session for guest: ${sessionId}, plan: ${planId}`);

    // 创建Stripe结账会话
    const stripeSession = await createStripeCheckoutSession({
      planId,
      amount: pricing.amount,
      currency: 'usd',
      customerEmail: contactInfo.email || null,
      customerPhone: contactInfo.phone || null,
      guestSessionId: sessionId,
      metadata: {
        isGuestOrder: 'true',
        sessionId,
        planId,
        imageData: imageData || '',
        style: style || '',
        contactPhone: contactInfo.phone || '',
        contactEmail: contactInfo.email || ''
      },
      successUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancelUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/checkout/cancel`
    });

    console.log(`✅ Stripe checkout session created: ${stripeSession.id}`);

    return NextResponse.json({
      success: true,
      checkoutUrl: stripeSession.url,
      sessionId: stripeSession.id,
      amount: pricing.amount,
      currency: pricing.currency
    });

  } catch (error) {
    console.error('❌ Guest checkout failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Checkout failed',
        code: 'CHECKOUT_ERROR'
      },
      { status: 500 }
    );
  }
}

// 定价配置
function getPricingForPlan(planId: string) {
  const pricing = {
    single: { amount: 490, currency: 'usd', credits: 1 }, // $4.90
    bundle: { amount: 1290, currency: 'usd', credits: 5 }, // $12.90
    basic: { amount: 990, currency: 'usd', credits: 20 }, // $9.90/month
    premium: { amount: 1990, currency: 'usd', credits: -1 } // $19.90/month (unlimited)
  };

  return pricing[planId as keyof typeof pricing] || null;
}
```

## 🎨 3. 前端组件实现

### 3.1 游客模式主组件
```typescript
// src/components/guest/GuestWeddingGenerator.tsx
'use client';

import { useState, useCallback } from 'react';
import { useGuestSession } from '@/hooks/useGuestSession';
import { StepGuide } from '@/components/ui/StepGuide';
import { ImageUpload } from '@/components/ui/ImageUpload';
import { StyleSelector } from '@/components/ui/StyleSelector';
import { PreviewDisplay } from '@/components/ui/PreviewDisplay';
import { QuickCheckout } from '@/components/checkout/QuickCheckout';
import { toast } from 'sonner';

interface GenerationState {
  uploadedImage: string | null;
  selectedStyle: string | null;
  previewImage: string | null;
  isGenerating: boolean;
  generationTime: number;
}

export function GuestWeddingGenerator() {
  const { session, canGeneratePreview, isLoading } = useGuestSession();
  const [state, setState] = useState<GenerationState>({
    uploadedImage: null,
    selectedStyle: null,
    previewImage: null,
    isGenerating: false,
    generationTime: 0
  });

  const handleImageUpload = useCallback((imageData: string) => {
    setState(prev => ({ ...prev, uploadedImage: imageData }));
    toast.success('照片上传成功！');
  }, []);

  const handleStyleSelect = useCallback((style: string) => {
    setState(prev => ({ ...prev, selectedStyle: style }));
  }, []);

  const handleGeneratePreview = useCallback(async () => {
    if (!session?.sessionId || !state.uploadedImage || !state.selectedStyle) {
      toast.error('请先上传照片并选择风格');
      return;
    }

    if (!canGeneratePreview) {
      toast.error('免费额度已用完，请升级获取更多生成次数');
      return;
    }

    setState(prev => ({ ...prev, isGenerating: true }));
    const startTime = Date.now();

    try {
      const response = await fetch('/api/guest/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId: session.sessionId,
          imageData: state.uploadedImage,
          style: state.selectedStyle
        })
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || '生成失败');
      }

      const generationTime = Date.now() - startTime;

      setState(prev => ({
        ...prev,
        previewImage: result.imageUrl,
        generationTime
      }));

      toast.success(`预览图生成成功！耗时 ${Math.round(generationTime / 1000)} 秒`);

    } catch (error) {
      console.error('Generation failed:', error);
      toast.error(error instanceof Error ? error.message : '生成失败，请重试');
    } finally {
      setState(prev => ({ ...prev, isGenerating: false }));
    }
  }, [session, state.uploadedImage, state.selectedStyle, canGeneratePreview]);

  const steps = [
    {
      id: 'upload',
      title: '上传您的照片',
      description: '上传一张清晰的正面照片，AI将为您生成专属婚纱照',
      component: (
        <ImageUpload
          onImageUpload={handleImageUpload}
          uploadedImage={state.uploadedImage}
          maxSize={10 * 1024 * 1024} // 10MB
          acceptedFormats={['image/jpeg', 'image/png', 'image/heic']}
        />
      ),
      canProceed: !!state.uploadedImage
    },
    {
      id: 'style',
      title: '选择婚纱风格',
      description: '从6种精美风格中选择您最喜欢的婚纱照风格',
      component: (
        <StyleSelector
          selectedStyle={state.selectedStyle}
          onStyleSelect={handleStyleSelect}
          showPreview={true}
        />
      ),
      canProceed: !!state.selectedStyle
    },
    {
      id: 'preview',
      title: '生成预览效果',
      description: '免费生成一张预览图，查看AI生成效果',
      component: (
        <PreviewDisplay
          uploadedImage={state.uploadedImage}
          selectedStyle={state.selectedStyle}
          previewImage={state.previewImage}
          isGenerating={state.isGenerating}
          generationTime={state.generationTime}
          canGenerate={canGeneratePreview}
          onGenerate={handleGeneratePreview}
        />
      ),
      canProceed: !!state.previewImage
    },
    {
      id: 'checkout',
      title: '获取高清版本',
      description: '升级到高清无水印版本，享受专业品质',
      component: (
        <QuickCheckout
          sessionId={session?.sessionId}
          previewImage={state.previewImage}
          imageData={state.uploadedImage}
          style={state.selectedStyle}
        />
      ),
      canProceed: true
    }
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        <span className="ml-2 text-gray-600">正在初始化...</span>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <StepGuide
        steps={steps}
        onComplete={() => {
          toast.success('感谢使用 Hera AI 婚纱照生成服务！');
        }}
      />
    </div>
  );
}
```

### 3.2 游客会话Hook
```typescript
// src/hooks/useGuestSession.ts
'use client';

import { useState, useEffect, useCallback } from 'react';
import { useLocalStorage } from './useLocalStorage';

interface GuestSession {
  sessionId: string;
  freeCreditsUsed: number;
  maxFreeCredits: number;
  expiresAt: string;
}

interface GuestSessionHook {
  session: GuestSession | null;
  canGeneratePreview: boolean;
  isLoading: boolean;
  refreshSession: () => Promise<void>;
  createNewSession: () => Promise<void>;
}

export function useGuestSession(): GuestSessionHook {
  const [session, setSession] = useLocalStorage<GuestSession | null>('hera_guest_session', null);
  const [isLoading, setIsLoading] = useState(true);

  const createNewSession = useCallback(async () => {
    try {
      const response = await fetch('/api/guest/session', {
        method: 'POST'
      });

      const data = await response.json();

      if (data.success) {
        const newSession: GuestSession = {
          sessionId: data.sessionId,
          freeCreditsUsed: 0,
          maxFreeCredits: data.maxFreeCredits,
          expiresAt: new Date(Date.now() + data.expiresIn * 1000).toISOString()
        };
        setSession(newSession);
        console.log('✅ Guest session created:', newSession.sessionId);
      } else {
        throw new Error(data.error || 'Failed to create session');
      }
    } catch (error) {
      console.error('❌ Failed to create guest session:', error);
      throw error;
    }
  }, [setSession]);

  const refreshSession = useCallback(async () => {
    if (!session?.sessionId) return;

    try {
      const response = await fetch(`/api/guest/session?sessionId=${session.sessionId}`);
      const data = await response.json();

      if (data.success) {
        setSession(prev => prev ? {
          ...prev,
          freeCreditsUsed: data.freeCreditsUsed,
          maxFreeCredits: data.maxFreeCredits
        } : null);
      } else {
        // 会话无效或过期，创建新会话
        await createNewSession();
      }
    } catch (error) {
      console.error('❌ Failed to refresh session:', error);
      // 出错时创建新会话
      await createNewSession();
    }
  }, [session?.sessionId, setSession, createNewSession]);

  // 检查会话是否过期
  const isSessionExpired = useCallback(() => {
    if (!session?.expiresAt) return true;
    return new Date(session.expiresAt) <= new Date();
  }, [session?.expiresAt]);

  // 初始化会话
  useEffect(() => {
    const initSession = async () => {
      setIsLoading(true);

      try {
        if (!session || isSessionExpired()) {
          await createNewSession();
        } else {
          await refreshSession();
        }
      } catch (error) {
        console.error('Session initialization failed:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initSession();
  }, []); // 只在组件挂载时执行

  const canGeneratePreview = session ?
    session.freeCreditsUsed < session.maxFreeCredits && !isSessionExpired() :
    false;

  return {
    session,
    canGeneratePreview,
    isLoading,
    refreshSession,
    createNewSession
  };
}
```

## 🚀 4. MVP实施计划

### 4.1 开发阶段划分

#### 第一周：基础架构搭建
**目标**: 完成核心系统架构和API开发

**任务清单**:
- [ ] 游客会话管理系统开发
- [ ] 数据库结构调整和迁移
- [ ] 基础API路由实现
- [ ] 速率限制和安全防护
- [ ] 图像处理工具开发

**验收标准**:
- 游客会话创建和管理功能正常
- API接口响应时间 < 2秒
- 安全防护机制生效
- 单元测试覆盖率 > 80%

#### 第二周：前端组件开发
**目标**: 完成用户界面和交互逻辑

**任务清单**:
- [ ] 游客模式主组件开发
- [ ] 步骤引导系统实现
- [ ] 图片上传和预览组件
- [ ] 风格选择界面优化
- [ ] 快速结账流程开发

**验收标准**:
- 用户流程完整可用
- 移动端适配良好
- 交互响应流畅
- 错误处理完善

#### 第三周：集成测试和优化
**目标**: 系统集成测试和性能优化

**任务清单**:
- [ ] 端到端测试用例编写
- [ ] 支付流程完整测试
- [ ] 性能瓶颈识别和优化
- [ ] 错误监控和日志系统
- [ ] 用户体验优化

**验收标准**:
- 所有核心功能测试通过
- 页面加载时间 < 3秒
- 支付成功率 > 95%
- 错误率 < 1%

#### 第四周：发布准备和上线
**目标**: 生产环境部署和监控

**任务清单**:
- [ ] 生产环境配置
- [ ] 安全审计和漏洞扫描
- [ ] 监控和告警系统配置
- [ ] 灰度发布策略实施
- [ ] 用户反馈收集机制

**验收标准**:
- 生产环境稳定运行
- 监控指标正常
- 用户反馈积极
- 转化率达到预期

### 4.2 技术栈和工具

#### 开发工具
```json
{
  "frontend": {
    "framework": "Next.js 15",
    "ui": "Radix UI + Tailwind CSS",
    "state": "React Context + Zustand",
    "forms": "React Hook Form + Zod",
    "testing": "Jest + React Testing Library"
  },
  "backend": {
    "runtime": "Node.js 20",
    "database": "PostgreSQL + Prisma",
    "cache": "Redis",
    "storage": "AWS S3 / 阿里云OSS",
    "monitoring": "Sentry + DataDog"
  },
  "ai_services": {
    "primary": "OpenAI DALL-E 3",
    "fallback": "HenAPI / APICore",
    "image_processing": "Sharp"
  },
  "payment": {
    "provider": "Stripe",
    "webhooks": "Stripe CLI",
    "security": "PCI DSS"
  }
}
```

#### 部署架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CDN (CloudFlare)   │    │   Load Balancer    │    │   App Servers      │
│                 │    │                 │    │                 │
│ • 静态资源缓存   │◄──►│ • SSL终止       │◄──►│ • Next.js应用    │
│ • 图片优化      │    │ • 健康检查      │    │ • API路由       │
│ • DDoS防护      │    │ • 流量分发      │    │ • 会话管理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Redis Cluster    │    │   PostgreSQL    │    │   File Storage     │
│                 │    │                 │    │                 │
│ • 会话存储      │    │ • 用户数据      │    │ • 图片存储      │
│ • 缓存层        │    │ • 订单记录      │    │ • 备份存储      │
│ • 速率限制      │    │ • 分析数据      │    │ • CDN分发       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 4.3 关键配置文件

#### 环境变量配置
```bash
# .env.production
# 基础配置
NEXT_PUBLIC_BASE_URL=https://hera-ai.com
NODE_ENV=production

# 数据库
DATABASE_URL=********************************/hera_prod
REDIS_URL=redis://redis-cluster:6379

# AI服务
OPENAI_API_KEY=sk-xxx
OPENAI_BASE_URL=https://api.openai.com/v1
AI_GENERATION_MODE=openai

# 支付
STRIPE_PRIVATE_KEY=sk_live_xxx
STRIPE_PUBLIC_KEY=pk_live_xxx
STRIPE_WEBHOOK_SECRET=whsec_xxx

# 存储
AWS_ACCESS_KEY_ID=xxx
AWS_SECRET_ACCESS_KEY=xxx
AWS_S3_BUCKET=hera-ai-images
AWS_REGION=us-east-1

# 监控
SENTRY_DSN=https://<EMAIL>/xxx
DATADOG_API_KEY=xxx

# 安全
JWT_SECRET=xxx
ENCRYPTION_KEY=xxx
```

#### Docker配置
```dockerfile
# Dockerfile
FROM node:20-alpine AS base

# 安装依赖
FROM base AS deps
WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN npm install -g pnpm && pnpm install --frozen-lockfile

# 构建应用
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm install -g pnpm && pnpm build

# 生产镜像
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000
ENV PORT=3000

CMD ["node", "server.js"]
```

## 📊 5. 监控和分析

### 5.1 关键指标监控
```typescript
// src/lib/analytics.ts
export interface AnalyticsEvent {
  event: string;
  properties: Record<string, any>;
  timestamp: number;
  sessionId?: string;
  userId?: string;
}

export class AnalyticsService {
  private events: AnalyticsEvent[] = [];

  track(event: string, properties: Record<string, any> = {}) {
    const analyticsEvent: AnalyticsEvent = {
      event,
      properties,
      timestamp: Date.now(),
      sessionId: this.getSessionId(),
      userId: this.getUserId()
    };

    this.events.push(analyticsEvent);
    this.sendToAnalytics(analyticsEvent);
  }

  // 关键业务指标
  trackGuestSessionCreated(sessionId: string) {
    this.track('guest_session_created', { sessionId });
  }

  trackImageUploaded(sessionId: string, fileSize: number, format: string) {
    this.track('image_uploaded', { sessionId, fileSize, format });
  }

  trackStyleSelected(sessionId: string, style: string) {
    this.track('style_selected', { sessionId, style });
  }

  trackPreviewGenerated(sessionId: string, style: string, generationTime: number) {
    this.track('preview_generated', {
      sessionId,
      style,
      generationTime,
      success: true
    });
  }

  trackCheckoutStarted(sessionId: string, planId: string, amount: number) {
    this.track('checkout_started', { sessionId, planId, amount });
  }

  trackPaymentCompleted(sessionId: string, planId: string, amount: number) {
    this.track('payment_completed', { sessionId, planId, amount });
  }

  trackConversion(sessionId: string, fromStep: string, toStep: string) {
    this.track('conversion', { sessionId, fromStep, toStep });
  }

  private async sendToAnalytics(event: AnalyticsEvent) {
    try {
      await fetch('/api/analytics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(event)
      });
    } catch (error) {
      console.error('Analytics tracking failed:', error);
    }
  }

  private getSessionId(): string | undefined {
    // 从localStorage或cookie获取会话ID
    return localStorage.getItem('hera_guest_session')?.sessionId;
  }

  private getUserId(): string | undefined {
    // 从认证状态获取用户ID
    return undefined; // 游客模式下为空
  }
}

export const analytics = new AnalyticsService();
```

### 5.2 性能监控
```typescript
// src/lib/performance-monitor.ts
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();

  startTimer(label: string): () => number {
    const startTime = performance.now();

    return () => {
      const duration = performance.now() - startTime;
      this.recordMetric(label, duration);
      return duration;
    };
  }

  recordMetric(label: string, value: number) {
    if (!this.metrics.has(label)) {
      this.metrics.set(label, []);
    }

    const values = this.metrics.get(label)!;
    values.push(value);

    // 保持最近100个数据点
    if (values.length > 100) {
      values.shift();
    }
  }

  getMetrics(label: string) {
    const values = this.metrics.get(label) || [];
    if (values.length === 0) return null;

    const sorted = [...values].sort((a, b) => a - b);
    return {
      count: values.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      avg: values.reduce((a, b) => a + b, 0) / values.length,
      p50: sorted[Math.floor(sorted.length * 0.5)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)]
    };
  }

  // 监控API响应时间
  async monitorApiCall<T>(
    label: string,
    apiCall: () => Promise<T>
  ): Promise<T> {
    const endTimer = this.startTimer(`api_${label}`);

    try {
      const result = await apiCall();
      const duration = endTimer();

      // 记录成功调用
      analytics.track('api_call_success', {
        endpoint: label,
        duration
      });

      return result;
    } catch (error) {
      const duration = endTimer();

      // 记录失败调用
      analytics.track('api_call_error', {
        endpoint: label,
        duration,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      throw error;
    }
  }

  // 监控组件渲染性能
  monitorComponentRender(componentName: string) {
    return {
      onRenderStart: () => {
        return this.startTimer(`render_${componentName}`);
      },
      onRenderEnd: (endTimer: () => number) => {
        const duration = endTimer();
        if (duration > 100) { // 超过100ms记录
          analytics.track('slow_render', {
            component: componentName,
            duration
          });
        }
      }
    };
  }
}

export const performanceMonitor = new PerformanceMonitor();
```

## 🔒 6. 安全和防护

### 6.1 速率限制实现
```typescript
// src/lib/rate-limit.ts
import { LRUCache } from 'lru-cache';

interface RateLimitOptions {
  interval: number; // 时间窗口（毫秒）
  uniqueTokenPerInterval: number; // 支持的唯一标识符数量
}

interface RateLimitResult {
  success: boolean;
  limit: number;
  remaining: number;
  reset: number;
}

export function rateLimit(options: RateLimitOptions) {
  const tokenCache = new LRUCache<string, number[]>({
    max: options.uniqueTokenPerInterval,
    ttl: options.interval,
  });

  return {
    check: async (limit: number, token: string): Promise<RateLimitResult> => {
      const now = Date.now();
      const windowStart = now - options.interval;

      const tokenHits = tokenCache.get(token) || [];

      // 清理过期的请求记录
      const validHits = tokenHits.filter(time => time > windowStart);

      const isAllowed = validHits.length < limit;

      if (isAllowed) {
        validHits.push(now);
        tokenCache.set(token, validHits);
      }

      return {
        success: isAllowed,
        limit,
        remaining: Math.max(0, limit - validHits.length),
        reset: now + options.interval
      };
    }
  };
}

// 预定义的限制器
export const guestSessionLimiter = rateLimit({
  interval: 60 * 1000, // 1分钟
  uniqueTokenPerInterval: 500
});

export const imageGenerationLimiter = rateLimit({
  interval: 60 * 60 * 1000, // 1小时
  uniqueTokenPerInterval: 1000
});

export const checkoutLimiter = rateLimit({
  interval: 5 * 60 * 1000, // 5分钟
  uniqueTokenPerInterval: 500
});
```

### 6.2 输入验证和清理
```typescript
// src/lib/validation.ts
import { z } from 'zod';

// 手机号验证
export function validatePhoneNumber(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

// 邮箱验证
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 图片文件验证
export const imageFileSchema = z.object({
  size: z.number().max(10 * 1024 * 1024, '文件大小不能超过10MB'),
  type: z.enum(['image/jpeg', 'image/png', 'image/heic'], {
    errorMap: () => ({ message: '只支持 JPEG、PNG、HEIC 格式' })
  })
});

// 会话ID验证
export const sessionIdSchema = z.string().uuid('无效的会话ID');

// 风格验证
export const styleSchema = z.enum([
  'chinese-traditional',
  'western-elegant',
  'beach-sunset',
  'forest-romantic',
  'vintage-classic',
  'modern-chic'
], {
  errorMap: () => ({ message: '不支持的风格类型' })
});

// 计划ID验证
export const planIdSchema = z.enum(['single', 'bundle', 'basic', 'premium'], {
  errorMap: () => ({ message: '无效的计划类型' })
});

// 联系信息验证
export const contactInfoSchema = z.object({
  phone: z.string().optional().refine(
    (phone) => !phone || validatePhoneNumber(phone),
    { message: '手机号格式不正确' }
  ),
  email: z.string().optional().refine(
    (email) => !email || validateEmail(email),
    { message: '邮箱格式不正确' }
  )
}).refine(
  (data) => data.phone || data.email,
  { message: '请提供手机号或邮箱' }
);

// 安全的HTML清理
export function sanitizeHtml(input: string): string {
  return input
    .replace(/[<>]/g, '') // 移除HTML标签
    .replace(/javascript:/gi, '') // 移除JavaScript协议
    .replace(/on\w+=/gi, '') // 移除事件处理器
    .trim();
}

// Base64图片验证
export function validateBase64Image(base64: string): boolean {
  const base64Regex = /^data:image\/(jpeg|jpg|png|heic);base64,/;
  return base64Regex.test(base64);
}
```

## 📋 7. 部署和运维

### 7.1 部署脚本
```bash
#!/bin/bash
# deploy.sh - MVP部署脚本

set -e

echo "🚀 开始部署 Hera AI MVP..."

# 环境变量检查
if [ -z "$DATABASE_URL" ]; then
  echo "❌ DATABASE_URL 环境变量未设置"
  exit 1
fi

if [ -z "$OPENAI_API_KEY" ]; then
  echo "❌ OPENAI_API_KEY 环境变量未设置"
  exit 1
fi

# 构建应用
echo "📦 构建应用..."
pnpm install --frozen-lockfile
pnpm build

# 数据库迁移
echo "🗄️ 执行数据库迁移..."
npx prisma migrate deploy

# 启动应用
echo "🎯 启动应用..."
pm2 start ecosystem.config.js --env production

# 健康检查
echo "🔍 执行健康检查..."
sleep 10
curl -f http://localhost:3000/api/health || exit 1

echo "✅ 部署完成！"
```

### 7.2 监控配置
```javascript
// ecosystem.config.js - PM2配置
module.exports = {
  apps: [{
    name: 'hera-ai-mvp',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

---

**文档状态**: ✅ 已完成
**下一步**: 开始第一周的基础架构开发
