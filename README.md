# Hera-Web - AI婚纱照生成平台

<div align="center">

![Hera-Web Logo](https://via.placeholder.com/200x80/FF69B4/FFFFFF?text=Hera-Web)

**🎨 让AI为您创造最美的婚纱回忆**

[![Next.js](https://img.shields.io/badge/Next.js-15.0-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-19.0-blue?style=flat-square&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind-3.4-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)
[![Prisma](https://img.shields.io/badge/Prisma-6.1-2D3748?style=flat-square&logo=prisma)](https://www.prisma.io/)

</div>

---

Hera-Web 是一个基于 Next.js 15 构建的AI婚纱照生成平台，采用最新的 React 技术栈和AI技术，为用户提供专业级的婚纱照生成服务。只需上传一张自拍照，AI就能为您生成多种风格的美丽婚纱照。

## 🌟 产品亮点

> **"从自拍到婚纱照，只需3分钟"**

- 🎯 **零门槛使用** - 无需专业摄影知识，人人都能创作
- 💎 **专业级品质** - 媲美影楼的4K高清婚纱照
- ⚡ **极速生成** - 2-3分钟完成，告别漫长等待
- 🌍 **全球服务** - 支持中英文，服务全球用户
- 🔒 **隐私保护** - 24小时自动删除，保护用户隐私

## 核心特性

### 🎨 AI婚纱照生成功能
- 📸 **智能照片上传** - 支持拖拽上传，自动验证图片格式和大小
- 🎭 **6种婚纱风格** - 中式传统、西式优雅、海滩日落、森林浪漫、复古经典、现代时尚
- ⚡ **快速生成** - 2-3分钟内完成AI婚纱照生成
- 🖼️ **高质量输出** - 专业级4K分辨率婚纱照
- 💾 **便捷管理** - 收藏、下载、分享功能一应俱全
- 🔒 **隐私保护** - 照片24小时后自动删除，确保用户隐私

### 🛠️ 技术特性
- 🚀 基于 Next.js 15 和 React 19 RC版本
- ⚡️ Turbopack 支持，提供极致开发体验
- 🎨 Radix UI + Tailwind CSS 构建的现代化UI
- 🌐 基于 next-intl 的国际化方案（中英文）
- 🔐 NextAuth.js v4 实现的身份认证
- 💳 Stripe 支付系统集成
- 📊 Prisma ORM 数据库管理
- 🔔 Sonner 提示系统
- 📱 响应式设计
- 🧪 自动化测试方案

## AI婚纱照功能详解

### 🎯 使用流程
1. **📸 上传照片** - 上传一张清晰的自拍照（支持JPEG、PNG、HEIC格式）
2. **🎨 选择风格** - 从6种精美婚纱风格中选择您喜欢的样式
3. **⏱️ AI生成** - 观看实时生成进度，通常2-3分钟完成
4. **🖼️ 获取结果** - 下载、收藏或分享您的专属婚纱照

### 🎭 可选婚纱风格

| 风格 | 描述 | 特色 |
|------|------|------|
| 🏮 **中式传统** | 优雅的传统中式婚纱 | 红金配色，古典韵味 |
| 👰 **西式优雅** | 经典白色婚纱 | 精致造型，永恒经典 |
| 🌅 **海滩日落** | 浪漫海滩婚礼 | 金色日落，自然浪漫 |
| 🌲 **森林浪漫** | 梦幻森林背景 | 自然光线，童话氛围 |
| 📷 **复古经典** | 永恒复古风格 | 怀旧色调，经典优雅 |
| ✨ **现代时尚** | 现代简约风格 | 简洁线条，时尚前卫 |

### 💎 服务特色
- **专业品质** - 无需昂贵摄影棚，获得工作室级别效果
- **多样选择** - 一次上传，体验6种不同婚纱风格
- **快速便捷** - 几分钟内完成，随时随地使用
- **安全私密** - 端到端加密，24小时后自动删除
- **全球服务** - 支持中英文，服务全球用户

## 技术栈

### 核心框架
- Next.js 15.0.3
- React 19.0.0-rc
- TypeScript 5.x

### AI & 图像处理
- 文件上传处理 (支持多格式)
- 图像预处理和验证
- AI模型集成接口
- 实时进度追踪

### UI框架
- Tailwind CSS 3.4.1
- Radix UI Components
  - Accordion
  - Dialog
  - Dropdown Menu
  - Slot
- Lucide React (图标)

### 状态管理与工具
- next-intl 3.26.3 (国际化)
- next-auth 4.24.11 (认证)
- Stripe 17.5.0 (支付)
- date-fns 4.1.0 (日期处理)
- UUID 11.0.4

### 数据库
- Prisma 6.1.0
- Prisma Client
- 照片上传记录表
- 生成任务管理表
- 生成结果存储表

### 测试工具
- Jest 29.7.0
- ts-jest 29.1.1

## 环境要求

- Node.js 18.17 或更高版本
- pnpm 8.0 或更高版本（推荐）
- MySQL 8.0 或更高版本（推荐）
- Docker和Docker Compose（用于测试环境）

## 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/wenhaofree/hera-web.git
cd hera-web
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 环境变量配置

```bash
cp .env.example .env.local
```

配置以下环境变量：

| 变量名 | 说明 | 示例 |
|-------|------|------|
| DATABASE_URL | 数据库连接URL | mysql://user:pass@host:3306/db |
| NEXTAUTH_SECRET | NextAuth.js 密钥 | your-secret-key |
| AUTH_GOOGLE_ID | Google OAuth ID | google-oauth-id |
| AUTH_GOOGLE_SECRET | Google OAuth Secret | google-oauth-secret |
| AUTH_GITHUB_ID | GitHub OAuth ID | github-oauth-id |
| AUTH_GITHUB_SECRET | GitHub OAuth Secret | github-oauth-secret |
| STRIPE_PUBLIC_KEY | Stripe 公钥 | pk_test_xxx |
| STRIPE_PRIVATE_KEY | Stripe 私钥 | sk_test_xxx |
| STRIPE_WEBHOOK_SECRET | Stripe Webhook 密钥 | whsec_xxx |
| AI_API_KEY | AI服务API密钥 | your-ai-api-key |
| AI_API_ENDPOINT | AI服务端点 | https://api.example.com |
| UPLOAD_MAX_SIZE | 最大上传文件大小 | 10485760 (10MB) |

### 4. 数据库初始化

```bash
# 拉取数据库架构
pnpm db:pull

# 推送架构变更
pnpm db:push

# 生成Prisma Client
pnpm db:generate

# 或者一键同步
pnpm db:sync
```

### 5. 启动开发服务器

```bash
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用

### 6. 体验AI婚纱照功能

1. 访问首页，点击"上传照片"按钮
2. 上传一张清晰的自拍照
3. 选择您喜欢的婚纱风格
4. 等待AI生成（2-3分钟）
5. 下载您的专属婚纱照

**注意**: 确保访问路径包含语言前缀，如：
- 中文版：`http://localhost:3000/zh`
- 英文版：`http://localhost:3000/en`

## 可用的脚本命令

```bash
# 开发环境启动（使用Turbopack）
pnpm dev

# 生产环境构建
pnpm build

# 生产环境启动
pnpm start

# ESLint 代码检查
pnpm lint

# Prisma 数据库操作
pnpm db:push     # 推送数据库变更
pnpm db:pull     # 拉取数据库架构
pnpm db:generate # 生成Prisma Client
pnpm db:studio   # 启动Prisma Studio
pnpm db:sync     # 同步数据库架构

# 测试相关命令
pnpm test:db            # 运行数据库测试
pnpm test:db:docker     # 使用Docker运行数据库测试
pnpm docker:up          # 启动Docker容器
pnpm docker:down        # 停止Docker容器

# 运行脚本步骤；-这一个目录就够
pnpm run db:test:init

# 推送架构到测试数据库
pnpm run db:test:push

# 生成Prisma客户端
pnpm run db:generate

# 或一键设置测试环境
pnpm run test:db:setup
# 运行所有数据库测试
pnpm run test:db
# 运行测试调试脚本
pnpm run test:debug

# 启动Prisma Studio查看测试数据库-http://localhost:5555
pnpm run db:test:studio
```

## 数据库配置注意：
1. sslmode=prefer 实现数据库链接；

## 认证配置注意事项

### GitHub OAuth认证配置

配置GitHub OAuth登录时，请注意以下关键事项：

1. **GitHub OAuth应用设置**
   - 在GitHub开发者设置页面 (https://github.com/settings/developers) 创建OAuth应用
   - 应用名称设置为您的项目名称，如："NextLaunchPad"
   - Homepage URL必须与环境变量中的`NEXT_PUBLIC_WEB_URL`保持一致

2. **回调URL配置**
   - 回调URL格式：`{您的域名}/api/auth/callback/github`
   - 本地开发环境示例：`http://localhost:3000/api/auth/callback/github`
   - **注意**：`localhost`和`127.0.0.1`在OAuth认证中被视为不同域名，必须精确匹配

3. **环境变量设置**
   ```
   # GitHub认证变量必须正确设置
   AUTH_GITHUB_ID=您的GitHub客户端ID
   AUTH_GITHUB_SECRET=您的GitHub客户端密钥
   NEXT_PUBLIC_AUTH_GITHUB_ENABLED=true
   
   # NEXTAUTH_URL与GitHub OAuth应用中的域名必须保持一致
   # 如果GitHub OAuth中使用localhost，这里也必须使用localhost
   NEXTAUTH_URL=http://localhost:3000
   NEXT_PUBLIC_WEB_URL=http://localhost:3000
   ```

4. **常见错误处理**
   - `redirect_uri is not associated with this application`：
     - 检查GitHub OAuth应用中的回调URL与实际使用的域名是否完全一致
     - 确保使用相同的域名格式（localhost vs 127.0.0.1）
     - 检查端口号是否匹配
   - `Missing GitHub client ID or secret`：
     - 确保环境变量中正确设置了GitHub认证凭据
     - 检查`AUTH_GITHUB_ID`和`AUTH_GITHUB_SECRET`是否与GitHub OAuth应用一致
   - `outgoing request timed out after 3500ms`（请求超时错误）：
     - 这通常是网络连接问题，而非配置错误
     - 检查您的网络连接是否稳定
     - 如果使用代理或VPN，尝试临时关闭
     - GitHub API可能暂时不可用，稍后再试
     - 如果在中国大陆地区，可能需要配置代理来访问GitHub API
     - 增加NextAuth超时配置（在auth.config.ts中添加`timeout: 10000`将超时延长到10秒）

5. **域名变更时的处理**
   - 当应用域名发生变更时（如从本地开发到生产环境）：
     - 更新GitHub OAuth应用中的回调URL
     - 或创建多个OAuth应用分别用于不同环境

## 数据库设计

### AI婚纱照相关表结构

```sql
-- 照片上传记录表
PhotoUpload {
  id          Int       @id @default(autoincrement())
  uuid        String    @unique
  userUuid    String?   # 用户UUID（支持匿名上传）
  filename    String    # 原始文件名
  fileUrl     String    # 文件存储路径
  fileSize    Int       # 文件大小
  mimeType    String    # 文件类型
  width       Int?      # 图片宽度
  height      Int?      # 图片高度
  expiresAt   DateTime? # 过期时间（24小时）
  createdAt   DateTime  @default(now())
}

-- 生成任务表
GenerationJob {
  id              Int       @id @default(autoincrement())
  uuid            String    @unique
  userUuid        String?   # 用户UUID
  photoUploadUuid String    # 关联上传照片
  selectedStyles  String    # 选择的风格（JSON）
  status          String    # queued/processing/completed/failed
  progress        Int       @default(0) # 进度百分比
  estimatedTime   Int?      # 预估时间（秒）
  createdAt       DateTime  @default(now())
}

-- 生成结果表
GeneratedPhoto {
  id           Int       @id @default(autoincrement())
  uuid         String    @unique
  jobUuid      String    # 关联生成任务
  styleId      String    # 风格ID
  styleName    String    # 风格名称
  imageUrl     String    # 生成图片URL
  thumbnailUrl String?   # 缩略图URL
  expiresAt    DateTime? # 过期时间
  createdAt    DateTime  @default(now())
}
```

## 数据库测试

项目包含了对数据库连接和表结构的自动化测试方案。

### 测试内容

- 数据库连接测试
- 表结构验证测试
- 字段类型和默认值测试
- 表关系测试
- 软删除功能测试
- AI婚纱照相关表的完整性测试

### 运行测试

使用本地数据库测试:

```bash
pnpm test:db
```

使用Docker独立环境测试（推荐）:

```bash
pnpm test:db:docker
```

这将：
1. 启动Docker容器中的PostgreSQL
2. 执行数据库迁移
3. 运行所有测试用例
4. 自动清理测试环境

## 部署

### Vercel 部署

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fwenhaofree%2Fhera-web&env=DATABASE_URL,NEXTAUTH_SECRET,AUTH_GOOGLE_ID,AUTH_GOOGLE_SECRET,AUTH_GITHUB_ID,AUTH_GITHUB_SECRET,STRIPE_PUBLIC_KEY,STRIPE_PRIVATE_KEY,STRIPE_WEBHOOK_SECRET,AI_API_KEY,AI_API_ENDPOINT&project-name=hera-web&repository-name=hera-web)

1. Fork 本项目
2. 在 Vercel 创建新项目
3. 导入你的 GitHub 仓库
4. 配置环境变量（包括AI服务相关配置）
5. 部署

### 部署注意事项

**AI服务配置**
- 确保AI API密钥和端点配置正确
- 设置合适的文件上传大小限制
- 配置图片存储服务（建议使用云存储）

**国际化配置**
- 确保域名支持多语言路径
- 配置正确的默认语言和回退语言

## AI婚纱照功能页面

### 🌐 国际化路由结构
```
/[locale]/              # 语言前缀 (en/zh)
├── upload/            # 照片上传页面
├── styles/            # 风格选择页面
├── generating/        # 生成进度页面
└── results/           # 结果展示页面
```

### 📱 功能页面详情

| 页面 | 路径 | 功能描述 |
|------|------|----------|
| 🏠 **首页** | `/[locale]/` | 产品介绍、功能展示、用户见证 |
| 📸 **上传页面** | `/[locale]/upload` | 拖拽上传、格式验证、预览功能 |
| 🎨 **风格选择** | `/[locale]/styles` | 6种风格展示、多选功能、预览 |
| ⏳ **生成进度** | `/[locale]/generating` | 实时进度、步骤指示、取消功能 |
| 🖼️ **结果展示** | `/[locale]/results` | 照片网格、下载分享、收藏功能 |

## 项目结构

```
hera-web/
├── src/
│   ├── app/                    # Next.js 应用目录
│   │   ├── api/               # API 路由
│   │   │   ├── upload/        # 照片上传API
│   │   │   └── generate/      # AI生成API
│   │   ├── [locale]/          # 国际化路由
│   │   │   ├── upload/        # 上传页面
│   │   │   ├── styles/        # 风格选择页面
│   │   │   ├── generating/    # 生成进度页面
│   │   │   ├── results/       # 结果展示页面
│   │   │   └── page.tsx       # 首页
│   │   └── layout.tsx         # 根布局
│   ├── components/            # React 组件
│   │   ├── ui/               # UI 组件
│   │   ├── sections/         # 页面区块组件
│   │   └── PhotoGallery.tsx  # 照片画廊组件
│   └── lib/                  # 工具函数
├── messages/                  # 国际化文件
│   ├── en.json               # 英文翻译
│   └── zh.json               # 中文翻译
├── prisma/                   # Prisma 配置
│   └── schema.prisma         # 数据库模型
├── public/                   # 静态资源
│   └── uploads/              # 上传文件存储
└── tests/                    # 测试文件目录
    ├── db/                   # 数据库测试
    │   ├── connection.test.ts # 连接测试
    │   └── schema.test.ts     # 表结构测试
    ├── setup.ts              # 测试环境设置
    ├── teardown.ts           # 测试环境清理
    └── jest.config.js        # Jest配置
```

## 🚀 功能路线图

### 已完成功能 ✅
- [x] 基础框架搭建（Next.js 15 + React 19）
- [x] 用户认证系统（NextAuth.js）
- [x] 支付系统集成（Stripe）
- [x] 国际化支持（中英文）
- [x] 照片上传功能
- [x] 6种婚纱风格选择
- [x] AI生成进度显示
- [x] 结果展示和下载
- [x] 响应式设计
- [x] 数据库设计和测试

### 计划中功能 🔄
- [ ] AI模型集成和优化
- [ ] 批量生成功能
- [ ] 高级编辑工具
- [ ] 社交分享优化
- [ ] 移动端APP
- [ ] 更多婚纱风格
- [ ] 视频生成功能
- [ ] API开放平台

### 长期规划 🎯
- [ ] 3D虚拟试衣
- [ ] AR实时预览
- [ ] 个性化风格定制
- [ ] 专业摄影师合作平台

## 贡献指南

我们欢迎所有形式的贡献！

### 如何贡献
1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 提交 Pull Request

### 贡献类型
- 🐛 Bug修复
- ✨ 新功能开发
- 📝 文档改进
- 🎨 UI/UX优化
- 🌐 国际化翻译
- 🧪 测试用例添加

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目作者：[WenHaoFree]
- Email：[<EMAIL>]
- GitHub：[https://github.com/wenhaofree]
- 项目地址：[https://github.com/wenhaofree/hera-web]

---

**🎨 让AI为您创造最美的婚纱回忆！**
