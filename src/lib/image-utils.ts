interface WatermarkConfig {
  enabled: boolean;
  text?: string;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
  opacity?: number;
  fontSize?: number;
  color?: string;
}

export async function addWatermark(imageUrl: string, watermarkConfig: WatermarkConfig): Promise<string> {
  try {
    if (!watermarkConfig.enabled) {
      return imageUrl;
    }

    // For now, we'll return the original image URL
    // In a production environment, you would implement actual watermarking
    // using a service like Sharp (server-side) or Canvas API (client-side)

    // This is a placeholder implementation
    // In production, you would:
    // 1. Download the image from imageUrl
    // 2. Create a watermark overlay
    // 3. Composite the watermark onto the image
    // 4. Upload the processed image to storage
    // 5. Return the new URL

    console.log('Adding watermark to image:', imageUrl);
    console.log('Watermark config:', {
      text: watermarkConfig.text || 'Hera AI Preview',
      position: watermarkConfig.position || 'bottom-right',
      opacity: watermarkConfig.opacity || 0.7,
      fontSize: watermarkConfig.fontSize || 24,
      color: watermarkConfig.color || '#ffffff'
    });

    // For demo purposes, return the original URL
    // In production, this would return the watermarked image URL
    return imageUrl;

  } catch (error) {
    console.error('Error adding watermark:', error);
    return imageUrl; // 失败时返回原图
  }
}

export async function compressImage(file: File, quality: number = 0.8): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算压缩后的尺寸
      const maxWidth = 1920;
      const maxHeight = 1080;
      let { width, height } = img;

      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制压缩后的图像
      ctx?.drawImage(img, 0, 0, width, height);

      canvas.toBlob((blob) => {
        if (blob) {
          const compressedFile = new File([blob], file.name, {
            type: 'image/jpeg',
            lastModified: Date.now()
          });
          resolve(compressedFile);
        } else {
          reject(new Error('Compression failed'));
        }
      }, 'image/jpeg', quality);
    };

    img.onerror = () => reject(new Error('Image load failed'));
    img.src = URL.createObjectURL(file);
  });
}

// 验证Base64图片
export function validateBase64Image(base64: string): boolean {
  const base64Regex = /^data:image\/(jpeg|jpg|png|heic);base64,/;
  return base64Regex.test(base64);
}

// 获取图片尺寸
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.naturalWidth, height: img.naturalHeight });
    };
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}
