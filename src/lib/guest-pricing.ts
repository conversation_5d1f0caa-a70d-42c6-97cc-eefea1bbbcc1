// Guest pricing configuration for hybrid monetization model

export interface GuestPricingPlan {
  id: string;
  name: string;
  nameZh: string;
  price: number;
  currency: string;
  credits: number;
  description: string;
  descriptionZh: string;
  features: string[];
  featuresZh: string[];
  popular?: boolean;
  badge?: string;
  badgeZh?: string;
}

export const GUEST_PRICING_PLANS: GuestPricingPlan[] = [
  {
    id: 'single',
    name: 'Single HD Photo',
    nameZh: '单张高清照片',
    price: 4.9,
    currency: 'USD',
    credits: 1,
    description: 'Perfect for trying out our service',
    descriptionZh: '体验我们服务的完美选择',
    features: [
      '1 high-quality photo (4K resolution)',
      'No watermark',
      'Commercial license included',
      'Instant download',
      'All 6 premium styles'
    ],
    featuresZh: [
      '1张高清照片（4K分辨率）',
      '无水印',
      '包含商用授权',
      '即时下载',
      '全部6种高级风格'
    ]
  },
  {
    id: 'bundle',
    name: 'Photo Bundle',
    nameZh: '照片套餐',
    price: 12.9,
    currency: 'USD',
    credits: 5,
    description: 'Best value for multiple photos',
    descriptionZh: '多张照片的最佳选择',
    popular: true,
    badge: 'Best Value',
    badgeZh: '最超值',
    features: [
      '5 high-quality photos (4K resolution)',
      'No watermark',
      'Commercial license included',
      'Bulk download',
      'All 6 premium styles',
      'Priority processing'
    ],
    featuresZh: [
      '5张高清照片（4K分辨率）',
      '无水印',
      '包含商用授权',
      '批量下载',
      '全部6种高级风格',
      '优先处理'
    ]
  },
  {
    id: 'basic',
    name: 'Basic Monthly',
    nameZh: '基础月度套餐',
    price: 9.9,
    currency: 'USD',
    credits: 20,
    description: 'For regular users',
    descriptionZh: '适合常规用户',
    features: [
      '20 photos per month',
      'High-quality 4K resolution',
      'No watermark',
      'All 6 premium styles',
      'Priority processing',
      'Email support'
    ],
    featuresZh: [
      '每月20张照片',
      '高质量4K分辨率',
      '无水印',
      '全部6种高级风格',
      '优先处理',
      '邮件支持'
    ]
  },
  {
    id: 'pro',
    name: 'Pro Monthly',
    nameZh: '专业月度套餐',
    price: 19.9,
    currency: 'USD',
    credits: -1, // Unlimited
    description: 'For power users',
    descriptionZh: '适合重度用户',
    badge: 'Most Popular',
    badgeZh: '最受欢迎',
    features: [
      'Unlimited photos per month',
      'Ultra high-quality 4K resolution',
      'No watermark',
      'All premium + exclusive styles',
      'Instant processing',
      'Style customization',
      'Priority support'
    ],
    featuresZh: [
      '每月无限张照片',
      '超高质量4K分辨率',
      '无水印',
      '全部高级+独家风格',
      '即时处理',
      '风格定制',
      '优先支持'
    ]
  }
];

// Pricing tiers for different user segments
export const PRICING_TIERS = {
  NEW_USER_DISCOUNT: 0.5, // 50% off for first-time users
  HOLIDAY_DISCOUNT: 0.3, // 30% off during holidays
  BULK_DISCOUNT: 0.2, // 20% off for bulk purchases
  YEARLY_DISCOUNT: 0.35 // 35% off for yearly subscriptions
};

// Dynamic pricing based on user behavior
export function calculateDynamicPrice(
  basePlan: GuestPricingPlan,
  userSegment: 'new' | 'returning' | 'premium',
  isHoliday: boolean = false
): number {
  let price = basePlan.price;
  
  // Apply new user discount
  if (userSegment === 'new') {
    price *= (1 - PRICING_TIERS.NEW_USER_DISCOUNT);
  }
  
  // Apply holiday discount
  if (isHoliday) {
    price *= (1 - PRICING_TIERS.HOLIDAY_DISCOUNT);
  }
  
  // Round to 2 decimal places
  return Math.round(price * 100) / 100;
}

// Get pricing plan by ID
export function getGuestPricingPlan(planId: string): GuestPricingPlan | null {
  return GUEST_PRICING_PLANS.find(plan => plan.id === planId) || null;
}

// Get recommended plan for guest users
export function getRecommendedPlan(): GuestPricingPlan {
  return GUEST_PRICING_PLANS.find(plan => plan.popular) || GUEST_PRICING_PLANS[0];
}

// Calculate savings compared to single photo purchases
export function calculateSavings(plan: GuestPricingPlan): number {
  const singlePhotoPrice = GUEST_PRICING_PLANS[0].price; // Single photo price
  if (plan.credits <= 1) return 0;
  
  const totalSinglePrice = singlePhotoPrice * plan.credits;
  const savings = totalSinglePrice - plan.price;
  return Math.round(savings * 100) / 100;
}
