import { LRUCache } from 'lru-cache';

interface RateLimitOptions {
  interval: number; // 时间窗口（毫秒）
  uniqueTokenPerInterval: number; // 支持的唯一标识符数量
}

interface RateLimitResult {
  success: boolean;
  limit: number;
  remaining: number;
  reset: number;
}

export function rateLimit(options: RateLimitOptions) {
  const tokenCache = new LRUCache<string, number[]>({
    max: options.uniqueTokenPerInterval,
    ttl: options.interval,
  });

  return {
    check: async (limit: number, token: string): Promise<RateLimitResult> => {
      const now = Date.now();
      const windowStart = now - options.interval;

      const tokenHits = tokenCache.get(token) || [];

      // 清理过期的请求记录
      const validHits = tokenHits.filter(time => time > windowStart);

      const isAllowed = validHits.length < limit;

      if (isAllowed) {
        validHits.push(now);
        tokenCache.set(token, validHits);
      }

      return {
        success: isAllowed,
        limit,
        remaining: Math.max(0, limit - validHits.length),
        reset: now + options.interval
      };
    }
  };
}

// 预定义的限制器
export const guestSessionLimiter = rateLimit({
  interval: 60 * 1000, // 1分钟
  uniqueTokenPerInterval: 500
});

export const imageGenerationLimiter = rateLimit({
  interval: 60 * 60 * 1000, // 1小时
  uniqueTokenPerInterval: 1000
});

export const checkoutLimiter = rateLimit({
  interval: 5 * 60 * 1000, // 5分钟
  uniqueTokenPerInterval: 500
});
