import { v4 as uuidv4 } from 'uuid';

export interface GuestSession {
  sessionId: string;
  uploadedImages: string[];
  generatedPreviews: number;
  freeCreditsUsed: number;
  maxFreeCredits: number;
  createdAt: Date;
  expiresAt: Date;
  ipAddress?: string;
  userAgent?: string;
  lastActivity: Date;
}

export class GuestSessionManager {
  private static instance: GuestSessionManager;
  private sessions: Map<string, GuestSession> = new Map();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // 每小时清理过期会话
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredSessions();
    }, 60 * 60 * 1000);
  }

  static getInstance(): GuestSessionManager {
    if (!GuestSessionManager.instance) {
      GuestSessionManager.instance = new GuestSessionManager();
    }
    return GuestSessionManager.instance;
  }

  createSession(ipAddress?: string, userAgent?: string): string {
    const sessionId = uuidv4();
    const now = new Date();
    
    const session: GuestSession = {
      sessionId,
      uploadedImages: [],
      generatedPreviews: 0,
      freeCreditsUsed: 0,
      maxFreeCredits: 1,
      createdAt: now,
      expiresAt: new Date(now.getTime() + 24 * 60 * 60 * 1000), // 24小时
      ipAddress,
      userAgent,
      lastActivity: now
    };
    
    this.sessions.set(sessionId, session);
    return sessionId;
  }

  getSession(sessionId: string): GuestSession | null {
    const session = this.sessions.get(sessionId);
    if (session && session.expiresAt > new Date()) {
      // 更新最后活动时间
      session.lastActivity = new Date();
      return session;
    }
    return null;
  }

  canGeneratePreview(sessionId: string): boolean {
    const session = this.getSession(sessionId);
    return session ? session.freeCreditsUsed < session.maxFreeCredits : false;
  }

  consumeFreeCredit(sessionId: string): boolean {
    const session = this.getSession(sessionId);
    if (session && this.canGeneratePreview(sessionId)) {
      session.freeCreditsUsed++;
      session.generatedPreviews++;
      session.lastActivity = new Date();
      return true;
    }
    return false;
  }

  addUploadedImage(sessionId: string, imageUrl: string): boolean {
    const session = this.getSession(sessionId);
    if (session) {
      session.uploadedImages.push(imageUrl);
      session.lastActivity = new Date();
      return true;
    }
    return false;
  }

  getRemainingCredits(sessionId: string): number {
    const session = this.getSession(sessionId);
    return session ? session.maxFreeCredits - session.freeCreditsUsed : 0;
  }

  private cleanupExpiredSessions(): void {
    const now = new Date();
    const expiredSessions: string[] = [];

    this.sessions.forEach((session, sessionId) => {
      if (session.expiresAt <= now) {
        expiredSessions.push(sessionId);
      }
    });

    expiredSessions.forEach(sessionId => {
      this.sessions.delete(sessionId);
    });
  }

  // 获取会话统计信息
  getSessionStats(): {
    totalSessions: number;
    activeSessions: number;
    totalPreviews: number;
  } {
    const now = new Date();
    let activeSessions = 0;
    let totalPreviews = 0;

    this.sessions.forEach((session) => {
      if (session.expiresAt > now) {
        activeSessions++;
      }
      totalPreviews += session.generatedPreviews;
    });

    return {
      totalSessions: this.sessions.size,
      activeSessions,
      totalPreviews
    };
  }
}

// 导出单例实例
export const guestSessionManager = GuestSessionManager.getInstance();
