import { z } from 'zod';

// 手机号验证
export function validatePhoneNumber(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

// 邮箱验证
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 图片文件验证
export const imageFileSchema = z.object({
  size: z.number().max(10 * 1024 * 1024, '文件大小不能超过10MB'),
  type: z.enum(['image/jpeg', 'image/png', 'image/heic'], {
    errorMap: () => ({ message: '只支持 JPEG、PNG、HEIC 格式' })
  })
});

// 会话ID验证
export const sessionIdSchema = z.string().uuid('无效的会话ID');

// 风格验证
export const styleSchema = z.enum([
  'chinese-traditional',
  'western-elegant',
  'beach-sunset',
  'forest-romantic',
  'vintage-classic',
  'modern-chic'
], {
  errorMap: () => ({ message: '不支持的风格类型' })
});

// 计划ID验证
export const planIdSchema = z.enum(['single', 'bundle', 'basic', 'premium'], {
  errorMap: () => ({ message: '无效的计划类型' })
});

// 联系信息验证
export const contactInfoSchema = z.object({
  phone: z.string().optional().refine(
    (phone) => !phone || validatePhoneNumber(phone),
    { message: '手机号格式不正确' }
  ),
  email: z.string().optional().refine(
    (email) => !email || validateEmail(email),
    { message: '邮箱格式不正确' }
  )
}).refine(
  (data) => data.phone || data.email,
  { message: '请提供手机号或邮箱' }
);

// 安全的HTML清理
export function sanitizeHtml(input: string): string {
  return input
    .replace(/[<>]/g, '') // 移除HTML标签
    .replace(/javascript:/gi, '') // 移除JavaScript协议
    .replace(/on\w+=/gi, '') // 移除事件处理器
    .trim();
}

// Base64图片验证
export function validateBase64Image(base64: string): boolean {
  const base64Regex = /^data:image\/(jpeg|jpg|png|heic);base64,/;
  return base64Regex.test(base64);
}
