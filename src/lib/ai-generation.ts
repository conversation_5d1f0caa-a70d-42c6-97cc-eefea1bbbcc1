import OpenAI from 'openai';
import { PREVIEW_CONFIG, PREMIUM_CONFIG, STYLE_PROMPTS, ImageConfig } from './preview-config';
import { addWatermark } from './image-utils';

interface GenerationRequest {
  imageData: string; // base64 encoded image
  style: keyof typeof STYLE_PROMPTS;
  config: ImageConfig;
  userId?: string;
  sessionId?: string;
}

interface GenerationResult {
  url: string;
  width: number;
  height: number;
  hasWatermark: boolean;
  generationTime: number;
}

export class AIGenerationService {
  private openai: OpenAI | null = null;

  constructor() {
    // Only initialize OpenAI client if API key is available
    if (process.env.OPENAI_API_KEY) {
      this.openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1'
      });
    }
  }

  private getOpenAIClient(): OpenAI {
    if (!this.openai) {
      throw new Error('OpenAI client not initialized. Please check your OPENAI_API_KEY environment variable.');
    }
    return this.openai;
  }

  async generateWeddingPhoto(request: GenerationRequest): Promise<GenerationResult> {
    const startTime = Date.now();

    try {
      const styleConfig = STYLE_PROMPTS[request.style];
      if (!styleConfig) {
        throw new Error(`Unsupported style: ${request.style}`);
      }

      // 构建完整的prompt
      const fullPrompt = `${styleConfig.prompt}, replace the person's face with the uploaded face while maintaining the pose and style`;

      console.log(`🎨 Generating ${request.config === PREVIEW_CONFIG ? 'preview' : 'premium'} image for style: ${request.style}`);

      // 调用OpenAI DALL-E API
      const openai = this.getOpenAIClient();
      const response = await openai.images.generate({
        model: "dall-e-3",
        prompt: fullPrompt,
        n: 1,
        size: this.mapResolutionToSize(request.config.resolution),
        quality: request.config.quality > 0.8 ? "hd" : "standard",
        response_format: "url"
      });

      if (!response.data || response.data.length === 0) {
        throw new Error('No image generated');
      }

      let imageUrl = response.data[0].url!;

      // 如果需要添加水印
      if (request.config.watermark.enabled) {
        imageUrl = await addWatermark(imageUrl, request.config.watermark);
      }

      const generationTime = Date.now() - startTime;

      console.log(`✅ Image generated successfully in ${generationTime}ms`);

      return {
        url: imageUrl,
        width: request.config.resolution.width,
        height: request.config.resolution.height,
        hasWatermark: request.config.watermark.enabled,
        generationTime
      };

    } catch (error) {
      console.error('❌ AI generation failed:', error);
      throw new Error(`Generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private mapResolutionToSize(resolution: { width: number; height: number }): "1024x1024" | "1792x1024" | "1024x1792" {
    // DALL-E 3 支持的尺寸
    if (resolution.width === resolution.height) {
      return "1024x1024";
    } else if (resolution.width > resolution.height) {
      return "1792x1024";
    } else {
      return "1024x1792";
    }
  }
}

// 导出单例实例
export const aiGenerationService = new AIGenerationService();

// 便捷函数
export async function generatePreviewImage(request: Omit<GenerationRequest, 'config'>): Promise<GenerationResult> {
  return aiGenerationService.generateWeddingPhoto({
    ...request,
    config: PREVIEW_CONFIG
  });
}

export async function generatePremiumImage(request: Omit<GenerationRequest, 'config'>): Promise<GenerationResult> {
  return aiGenerationService.generateWeddingPhoto({
    ...request,
    config: PREMIUM_CONFIG
  });
}
