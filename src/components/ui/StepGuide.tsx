'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Card } from '@/components/ui/card';
import { ChevronLeft, ChevronRight, Sparkles, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Step {
  id: string;
  title: string;
  description: string;
  component: React.ReactNode;
  canProceed: boolean;
  isComplete?: boolean;
}

interface StepGuideProps {
  steps: Step[];
  onComplete: () => void;
  onStepChange?: (stepIndex: number) => void;
  className?: string;
  autoScroll?: boolean;
}

export function StepGuide({ 
  steps, 
  onComplete, 
  onStepChange,
  className,
  autoScroll = true 
}: StepGuideProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const stepRefs = useRef<(HTMLDivElement | null)[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  const progress = ((currentStep + 1) / steps.length) * 100;
  const step = steps[currentStep];

  // Smooth scroll to current step
  const scrollToStep = (stepIndex: number) => {
    if (!autoScroll) return;
    
    const stepElement = stepRefs.current[stepIndex];
    if (stepElement) {
      stepElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      });
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      onStepChange?.(nextStep);
      
      // Delay scroll to allow state update
      setTimeout(() => scrollToStep(nextStep), 100);
    } else {
      onComplete();
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      onStepChange?.(prevStep);
      
      // Delay scroll to allow state update
      setTimeout(() => scrollToStep(prevStep), 100);
    }
  };

  const handleStepClick = (stepIndex: number) => {
    // Only allow clicking on completed steps or the current step
    if (stepIndex <= currentStep) {
      setCurrentStep(stepIndex);
      onStepChange?.(stepIndex);
      setTimeout(() => scrollToStep(stepIndex), 100);
    }
  };

  // Auto-scroll when component mounts
  useEffect(() => {
    if (autoScroll) {
      setTimeout(() => scrollToStep(currentStep), 300);
    }
  }, []);

  return (
    <div ref={containerRef} className={cn("max-w-4xl mx-auto", className)}>
      {/* Progress Header */}
      <div className="mb-8 sticky top-20 bg-white/95 backdrop-blur-sm z-10 p-4 rounded-lg shadow-sm border">
        <div className="flex justify-between items-center mb-3">
          <span className="text-sm font-medium text-gray-600">
            步骤 {currentStep + 1} / {steps.length}
          </span>
          <span className="text-sm text-gray-500">
            {Math.round(progress)}% 完成
          </span>
        </div>
        <Progress value={progress} className="h-2 mb-4" />
        
        {/* Step Indicators */}
        <div className="flex items-center justify-between">
          {steps.map((stepItem, index) => (
            <button
              key={stepItem.id}
              onClick={() => handleStepClick(index)}
              className={cn(
                "flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200",
                "text-sm font-medium",
                index === currentStep && "bg-purple-100 text-purple-700",
                index < currentStep && "bg-green-100 text-green-700 cursor-pointer hover:bg-green-200",
                index > currentStep && "text-gray-400 cursor-not-allowed"
              )}
              disabled={index > currentStep}
            >
              <div className={cn(
                "w-6 h-6 rounded-full flex items-center justify-center text-xs",
                index === currentStep && "bg-purple-500 text-white",
                index < currentStep && "bg-green-500 text-white",
                index > currentStep && "bg-gray-200 text-gray-400"
              )}>
                {index < currentStep ? (
                  <CheckCircle className="w-4 h-4" />
                ) : (
                  index + 1
                )}
              </div>
              <span className="hidden sm:inline">{stepItem.title}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="space-y-8">
        {steps.map((stepItem, index) => (
          <div
            key={stepItem.id}
            ref={(el) => {
              stepRefs.current[index] = el;
            }}
            className={cn(
              "transition-all duration-500",
              index === currentStep ? "opacity-100 scale-100" : "opacity-50 scale-95 pointer-events-none"
            )}
          >
            <Card className={cn(
              "p-8 border-2 transition-all duration-300",
              index === currentStep && "border-purple-200 shadow-lg",
              index < currentStep && "border-green-200 bg-green-50/50",
              index > currentStep && "border-gray-200"
            )}>
              {/* Step Header */}
              <div className="text-center mb-8">
                <div className="flex items-center justify-center mb-4">
                  <div className={cn(
                    "w-12 h-12 rounded-full flex items-center justify-center mr-3 transition-all duration-300",
                    index === currentStep && "bg-purple-100 text-purple-600",
                    index < currentStep && "bg-green-100 text-green-600",
                    index > currentStep && "bg-gray-100 text-gray-400"
                  )}>
                    {index < currentStep ? (
                      <CheckCircle className="w-6 h-6" />
                    ) : (
                      <Sparkles className="w-6 h-6" />
                    )}
                  </div>
                  <h2 className="text-2xl font-bold">{stepItem.title}</h2>
                </div>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  {stepItem.description}
                </p>
              </div>

              {/* Step Component */}
              <div className="mb-8">
                {stepItem.component}
              </div>

              {/* Step Status */}
              {index < currentStep && (
                <div className="text-center">
                  <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    已完成
                  </div>
                </div>
              )}
            </Card>
          </div>
        ))}
      </div>

      {/* Navigation */}
      <div className="flex justify-between mt-8 sticky bottom-4 bg-white/95 backdrop-blur-sm p-4 rounded-lg shadow-sm border">
        <Button
          variant="outline"
          onClick={handlePrev}
          disabled={currentStep === 0}
          className="flex items-center"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          上一步
        </Button>

        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <span>{currentStep + 1}</span>
          <span>/</span>
          <span>{steps.length}</span>
        </div>

        <Button
          onClick={handleNext}
          disabled={!step.canProceed}
          className={cn(
            "flex items-center transition-all duration-200",
            step.canProceed 
              ? "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600" 
              : "opacity-50 cursor-not-allowed"
          )}
        >
          {currentStep === steps.length - 1 ? '完成' : '下一步'}
          {currentStep !== steps.length - 1 && (
            <ChevronRight className="h-4 w-4 ml-2" />
          )}
        </Button>
      </div>
    </div>
  );
}
