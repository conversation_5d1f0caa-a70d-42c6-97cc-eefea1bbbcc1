'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Check, Sparkles } from 'lucide-react';

interface StyleOption {
  id: string;
  name: string;
  nameZh: string;
  description: string;
  descriptionZh: string;
  previewImage: string;
  popular?: boolean;
  new?: boolean;
}

interface StyleSelectorProps {
  selectedStyle: string | null;
  onStyleSelect: (style: string) => void;
  showPreview?: boolean;
  className?: string;
}

const WEDDING_STYLES: StyleOption[] = [
  {
    id: 'chinese-traditional',
    name: 'Chinese Traditional',
    nameZh: '中式传统',
    description: 'Elegant traditional Chinese wedding dress with red and gold colors',
    descriptionZh: '优雅的传统中式婚纱，红金配色，古典韵味',
    previewImage: '/images/styles/chinese-traditional.jpg',
    popular: true
  },
  {
    id: 'western-elegant',
    name: 'Western Elegant',
    nameZh: '西式优雅',
    description: 'Classic white wedding dress with timeless elegance',
    descriptionZh: '经典白色婚纱，永恒优雅',
    previewImage: '/images/styles/western-elegant.jpg'
  },
  {
    id: 'beach-sunset',
    name: 'Beach Sunset',
    nameZh: '海滩日落',
    description: 'Romantic beach wedding with golden sunset background',
    descriptionZh: '浪漫海滩婚礼，金色日落背景',
    previewImage: '/images/styles/beach-sunset.jpg'
  },
  {
    id: 'forest-romantic',
    name: 'Forest Romantic',
    nameZh: '森林浪漫',
    description: 'Dreamy forest setting with natural lighting',
    descriptionZh: '梦幻森林背景，自然光线',
    previewImage: '/images/styles/forest-romantic.jpg',
    new: true
  },
  {
    id: 'vintage-classic',
    name: 'Vintage Classic',
    nameZh: '复古经典',
    description: 'Timeless vintage style with classic elegance',
    descriptionZh: '永恒复古风格，经典优雅',
    previewImage: '/images/styles/vintage-classic.jpg'
  },
  {
    id: 'modern-chic',
    name: 'Modern Chic',
    nameZh: '现代时尚',
    description: 'Contemporary minimalist style with clean lines',
    descriptionZh: '现代简约风格，简洁线条',
    previewImage: '/images/styles/modern-chic.jpg'
  }
];

export function StyleSelector({
  selectedStyle,
  onStyleSelect,
  showPreview = true,
  className
}: StyleSelectorProps) {
  const [hoveredStyle, setHoveredStyle] = useState<string | null>(null);

  return (
    <div className={cn("w-full max-w-6xl mx-auto", className)}>
      {/* Header */}
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold mb-2">选择您喜欢的婚纱风格</h3>
        <p className="text-gray-600">
          从6种精美风格中选择，每种风格都经过专业调优
        </p>
      </div>

      {/* Style Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {WEDDING_STYLES.map((style) => (
          <Card
            key={style.id}
            className={cn(
              "relative cursor-pointer transition-all duration-300 hover:scale-105",
              "border-2 overflow-hidden group",
              selectedStyle === style.id 
                ? "border-purple-500 ring-2 ring-purple-200 shadow-lg" 
                : "border-gray-200 hover:border-purple-300"
            )}
            onClick={() => onStyleSelect(style.id)}
            onMouseEnter={() => setHoveredStyle(style.id)}
            onMouseLeave={() => setHoveredStyle(null)}
          >
            {/* Badges */}
            <div className="absolute top-3 left-3 z-10 flex gap-2">
              {style.popular && (
                <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                  热门
                </Badge>
              )}
              {style.new && (
                <Badge className="bg-green-500 text-white">
                  新品
                </Badge>
              )}
            </div>

            {/* Selection Indicator */}
            {selectedStyle === style.id && (
              <div className="absolute top-3 right-3 z-10">
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                  <Check className="h-5 w-5 text-white" />
                </div>
              </div>
            )}

            {/* Preview Image */}
            <div className="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
              {/* Placeholder for actual images */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <Sparkles className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">{style.nameZh}</p>
                </div>
              </div>
              
              {/* Hover Overlay */}
              <div className={cn(
                "absolute inset-0 bg-black/20 transition-opacity duration-300",
                hoveredStyle === style.id ? "opacity-100" : "opacity-0"
              )}>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="bg-white/90 rounded-lg px-4 py-2">
                    <p className="text-sm font-medium text-gray-900">点击选择</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Style Info */}
            <div className="p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-semibold text-gray-900">{style.nameZh}</h4>
                <span className="text-xs text-gray-500">{style.name}</span>
              </div>
              <p className="text-sm text-gray-600 line-clamp-2">
                {style.descriptionZh}
              </p>
            </div>

            {/* Selection Animation */}
            <div className={cn(
              "absolute inset-0 border-2 border-purple-500 rounded-lg transition-all duration-300",
              selectedStyle === style.id 
                ? "opacity-100 animate-pulse" 
                : "opacity-0"
            )} />
          </Card>
        ))}
      </div>

      {/* Selected Style Preview */}
      {selectedStyle && showPreview && (
        <div className="mt-8 text-center">
          <div className="inline-flex items-center px-6 py-3 bg-purple-100 text-purple-700 rounded-full">
            <Check className="h-5 w-5 mr-2" />
            <span className="font-medium">
              已选择：{WEDDING_STYLES.find(s => s.id === selectedStyle)?.nameZh}
            </span>
          </div>
        </div>
      )}

      {/* Style Tips */}
      <div className="mt-8 text-center">
        <p className="text-sm text-gray-500 mb-2">💡 选择小贴士：</p>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 text-xs text-gray-400">
          <span>🎨 根据个人喜好选择</span>
          <span>📸 考虑照片背景</span>
          <span>✨ 可随时更换风格</span>
        </div>
      </div>
    </div>
  );
}
