'use client';

import { useState, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Upload, Image as ImageIcon, X, CheckCircle, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface ImageUploadProps {
  onImageUpload: (imageData: string) => void;
  uploadedImage?: string | null;
  maxSize?: number; // in bytes
  acceptedFormats?: string[];
  className?: string;
}

export function ImageUpload({
  onImageUpload,
  uploadedImage,
  maxSize = 10 * 1024 * 1024, // 10MB
  acceptedFormats = ['image/jpeg', 'image/png', 'image/heic'],
  className
}: ImageUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [imageQuality, setImageQuality] = useState<'good' | 'fair' | 'poor' | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    if (!acceptedFormats.includes(file.type)) {
      return '不支持的文件格式。请上传 JPEG、PNG 或 HEIC 格式的图片。';
    }
    
    if (file.size > maxSize) {
      return `文件大小超过限制。请上传小于 ${Math.round(maxSize / 1024 / 1024)}MB 的图片。`;
    }
    
    return null;
  };

  const assessImageQuality = (file: File, img: HTMLImageElement) => {
    const { width, height } = img;
    const fileSize = file.size;
    
    // Quality assessment based on resolution and file size
    if (width >= 1024 && height >= 1024 && fileSize > 500 * 1024) {
      setImageQuality('good');
    } else if (width >= 512 && height >= 512 && fileSize > 200 * 1024) {
      setImageQuality('fair');
    } else {
      setImageQuality('poor');
    }
  };

  const processFile = useCallback((file: File) => {
    const error = validateFile(file);
    if (error) {
      toast.error(error);
      return;
    }

    setIsProcessing(true);
    setUploadProgress(0);

    const reader = new FileReader();
    
    reader.onprogress = (e) => {
      if (e.lengthComputable) {
        setUploadProgress((e.loaded / e.total) * 100);
      }
    };

    reader.onloadend = () => {
      const result = reader.result as string;
      
      // Create image element for quality assessment
      const img = new Image();
      img.onload = () => {
        assessImageQuality(file, img);
        setIsProcessing(false);
        setUploadProgress(100);
        onImageUpload(result);
        toast.success('照片上传成功！');
      };
      img.src = result;
    };

    reader.onerror = () => {
      setIsProcessing(false);
      toast.error('文件读取失败，请重试。');
    };

    reader.readAsDataURL(file);
  }, [onImageUpload, maxSize, acceptedFormats]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      processFile(file);
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      processFile(files[0]);
    }
  }, [processFile]);

  const clearImage = () => {
    setImageQuality(null);
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const getQualityColor = () => {
    switch (imageQuality) {
      case 'good':
        return 'text-green-600 bg-green-100 border-green-200';
      case 'fair':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'poor':
        return 'text-red-600 bg-red-100 border-red-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getQualityIcon = () => {
    switch (imageQuality) {
      case 'good':
        return <CheckCircle className="h-4 w-4" />;
      case 'fair':
        return <AlertCircle className="h-4 w-4" />;
      case 'poor':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return null;
    }
  };

  const getQualityText = () => {
    switch (imageQuality) {
      case 'good':
        return '图片质量优秀，适合生成高质量婚纱照';
      case 'fair':
        return '图片质量良好，建议使用更高分辨率的照片';
      case 'poor':
        return '图片质量较低，可能影响生成效果';
      default:
        return '';
    }
  };

  return (
    <div className={cn("w-full max-w-2xl mx-auto", className)}>
      <Card className={cn(
        "relative border-2 border-dashed transition-all duration-300",
        isDragging && "border-purple-400 bg-purple-50",
        uploadedImage && "border-green-400 bg-green-50",
        !uploadedImage && !isDragging && "border-gray-300 hover:border-gray-400"
      )}>
        <div
          className="p-8 text-center cursor-pointer"
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          {uploadedImage ? (
            <div className="space-y-4">
              <div className="relative inline-block">
                <img
                  src={uploadedImage}
                  alt="Uploaded"
                  className="max-w-full max-h-64 rounded-lg shadow-md"
                />
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute top-2 right-2 bg-white/90 hover:bg-white"
                  onClick={(e) => {
                    e.stopPropagation();
                    clearImage();
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              
              {imageQuality && (
                <div className={cn(
                  "inline-flex items-center px-3 py-2 rounded-full text-sm font-medium border",
                  getQualityColor()
                )}>
                  {getQualityIcon()}
                  <span className="ml-2">{getQualityText()}</span>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <div className={cn(
                "w-16 h-16 mx-auto rounded-full flex items-center justify-center transition-colors",
                isDragging ? "bg-purple-100 text-purple-600" : "bg-gray-100 text-gray-400"
              )}>
                <Upload className="h-8 w-8" />
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">
                  {isDragging ? "松开以上传照片" : "上传您的照片"}
                </h3>
                <p className="text-sm text-gray-600">
                  拖拽照片到此处，或点击选择文件
                </p>
                <p className="text-xs text-gray-500">
                  支持 JPEG、PNG、HEIC 格式，最大 {Math.round(maxSize / 1024 / 1024)}MB
                </p>
              </div>
            </div>
          )}

          {isProcessing && (
            <div className="mt-4 space-y-2">
              <Progress value={uploadProgress} className="h-2" />
              <p className="text-sm text-gray-600">
                正在处理照片... {Math.round(uploadProgress)}%
              </p>
            </div>
          )}
        </div>

        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          accept={acceptedFormats.join(',')}
          onChange={handleFileChange}
        />
      </Card>

      {/* Upload Tips */}
      <div className="mt-4 text-center">
        <p className="text-sm text-gray-500 mb-2">💡 拍照小贴士：</p>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 text-xs text-gray-400">
          <span>📸 正面清晰照片</span>
          <span>💡 光线充足</span>
          <span>🎯 面部居中</span>
        </div>
      </div>
    </div>
  );
}
