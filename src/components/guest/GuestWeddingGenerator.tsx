'use client';

import { useState, useCallback } from 'react';
import { useGuestSession } from '@/hooks/useGuestSession';
import { StepGuide } from '@/components/ui/StepGuide';
import { ImageUpload } from '@/components/ui/ImageUpload';
import { StyleSelector } from '@/components/ui/StyleSelector';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Upload, Palette, Eye, CreditCard, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface GenerationState {
  uploadedImage: string | null;
  selectedStyle: string | null;
  previewImage: string | null;
  isGenerating: boolean;
  generationTime: number;
}

export function GuestWeddingGenerator() {
  const { session, canGeneratePreview, isLoading } = useGuestSession();
  const [state, setState] = useState<GenerationState>({
    uploadedImage: null,
    selectedStyle: null,
    previewImage: null,
    isGenerating: false,
    generationTime: 0
  });

  const handleImageUpload = useCallback((imageData: string) => {
    setState(prev => ({ ...prev, uploadedImage: imageData }));
    toast.success('照片上传成功！');
  }, []);

  const handleStyleSelect = useCallback((style: string) => {
    setState(prev => ({ ...prev, selectedStyle: style }));
  }, []);

  const handleGeneratePreview = useCallback(async () => {
    if (!session?.sessionId || !state.uploadedImage || !state.selectedStyle) {
      toast.error('请先上传照片并选择风格');
      return;
    }

    if (!canGeneratePreview) {
      toast.error('免费额度已用完，请升级获取更多生成次数');
      return;
    }

    setState(prev => ({ ...prev, isGenerating: true }));
    const startTime = Date.now();

    try {
      const response = await fetch('/api/guest/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId: session.sessionId,
          imageData: state.uploadedImage,
          style: state.selectedStyle
        })
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || '生成失败');
      }

      const generationTime = Date.now() - startTime;

      setState(prev => ({
        ...prev,
        previewImage: result.imageUrl,
        generationTime
      }));

      toast.success(`预览图生成成功！耗时 ${Math.round(generationTime / 1000)} 秒`);

    } catch (error) {
      console.error('Generation failed:', error);
      toast.error(error instanceof Error ? error.message : '生成失败，请重试');
    } finally {
      setState(prev => ({ ...prev, isGenerating: false }));
    }
  }, [session, state.uploadedImage, state.selectedStyle, canGeneratePreview]);

  const steps = [
    {
      id: 'upload',
      title: '上传您的照片',
      description: '上传一张清晰的正面照片，AI将为您生成专属婚纱照',
      component: (
        <ImageUpload
          onImageUpload={handleImageUpload}
          uploadedImage={state.uploadedImage}
          maxSize={10 * 1024 * 1024} // 10MB
          acceptedFormats={['image/jpeg', 'image/png', 'image/heic']}
        />
      ),
      canProceed: !!state.uploadedImage
    },
    {
      id: 'style',
      title: '选择婚纱风格',
      description: '从6种精美风格中选择您最喜欢的婚纱照风格',
      component: (
        <StyleSelector
          selectedStyle={state.selectedStyle}
          onStyleSelect={handleStyleSelect}
          showPreview={true}
        />
      ),
      canProceed: !!state.selectedStyle
    },
    {
      id: 'preview',
      title: '生成预览效果',
      description: '免费生成一张预览图，查看AI生成效果',
      component: (
        <div className="text-center space-y-6">
          {state.previewImage ? (
            <div className="space-y-4">
              <img
                src={state.previewImage}
                alt="Generated preview"
                className="max-w-md mx-auto rounded-lg shadow-lg"
              />
              <div className="text-sm text-gray-600">
                生成时间: {Math.round(state.generationTime / 1000)} 秒
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-gray-600">
                {canGeneratePreview
                  ? '准备生成您的专属婚纱照预览'
                  : '免费额度已用完，请升级获取更多生成次数'
                }
              </div>
              <Button
                onClick={handleGeneratePreview}
                disabled={state.isGenerating || !canGeneratePreview}
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
              >
                {state.isGenerating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    生成中...
                  </>
                ) : (
                  '生成预览'
                )}
              </Button>
            </div>
          )}
        </div>
      ),
      canProceed: !!state.previewImage
    },
    {
      id: 'checkout',
      title: '获取高清版本',
      description: '升级到高清无水印版本，享受专业品质',
      component: (
        <div className="text-center space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
            <Card className="p-4 border-2 border-purple-200">
              <div className="text-2xl font-bold text-purple-600">$4.9</div>
              <div className="text-sm text-gray-600">单张高清图</div>
            </Card>
            <Card className="p-4 border-2 border-green-200 bg-green-50">
              <div className="text-2xl font-bold text-green-600">$12.9</div>
              <div className="text-sm text-gray-600">5张套餐</div>
              <div className="text-xs text-green-600 font-medium">最超值</div>
            </Card>
            <Card className="p-4 border-2 border-blue-200">
              <div className="text-2xl font-bold text-blue-600">$9.9</div>
              <div className="text-sm text-gray-600">月度会员</div>
            </Card>
          </div>
          <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600">
            立即购买高清版本
          </Button>
        </div>
      ),
      canProceed: true
    }
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        <span className="ml-2 text-gray-600">正在初始化...</span>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <StepGuide
        steps={steps}
        onComplete={() => {
          toast.success('感谢使用 Hera AI 婚纱照生成服务！');
        }}
        autoScroll={true}
      />
    </div>
  );
}
