"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { SUBSCRIPTION_PLANS } from "@/lib/subscription"
import { SubscriptionPricingCard } from "@/components/ui/subscription-pricing-card"
import { Tab } from "@/components/ui/pricing-tab"

interface PricingSectionProps {
  title: string
  subtitle: string
}

export function PricingSection({
  title,
  subtitle,
}: PricingSectionProps) {
  const [selectedFrequency, setSelectedFrequency] = React.useState("monthly")
  const pathname = usePathname()
  const currentLocale = pathname.split('/')[1] || 'en'

  // Get all plans including free trial
  const allPlans = SUBSCRIPTION_PLANS

  return (
    <section className="flex flex-col items-center gap-12 py-16 px-4">
      {/* Header */}
      <div className="space-y-8 text-center max-w-3xl mx-auto">
        <div className="space-y-4">
          <h1 className="text-4xl font-bold md:text-5xl lg:text-6xl bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
            {title}
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            {subtitle}
          </p>
        </div>

        {/* Frequency Toggle */}
        <div className="mx-auto flex w-fit rounded-full bg-gray-100 p-1.5 shadow-inner">
          <Tab
            text={currentLocale === 'zh' ? '月付' : 'Monthly'}
            selected={selectedFrequency === "monthly"}
            setSelected={() => setSelectedFrequency("monthly")}
          />
          <Tab
            text={currentLocale === 'zh' ? '年付' : 'Yearly'}
            selected={selectedFrequency === "yearly"}
            setSelected={() => setSelectedFrequency("yearly")}
            discount={true}
          />
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="w-full max-w-8xl mx-auto px-4">
        <div className="grid gap-4 sm:gap-6 lg:gap-8 grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 items-stretch justify-items-center">
          {allPlans.map((plan, index) => (
            <div
              key={plan.id}
              className={cn(
                "w-full max-w-xs transition-all duration-300",
                "animate-fade-in-up"
              )}
              style={{
                animationDelay: `${index * 100}ms`,
                animationFillMode: 'both'
              }}
            >
              <SubscriptionPricingCard
                plan={plan}
                paymentFrequency={selectedFrequency}
                locale={currentLocale}
              />
            </div>
          ))}
        </div>

        {/* Mobile scroll hint */}
        <div className="xl:hidden text-center mt-6">
          <p className="text-sm text-gray-500">
            {currentLocale === 'zh'
              ? '← 滑动查看更多方案 →'
              : '← Swipe to see more plans →'
            }
          </p>
        </div>
      </div>

      {/* Trust indicators */}
      <div className="text-center space-y-4 max-w-2xl mx-auto">
        <p className="text-sm text-gray-500">
          {currentLocale === 'zh'
            ? '✓ 30天退款保证 ✓ 安全支付 ✓ 随时取消订阅'
            : '✓ 30-day money-back guarantee ✓ Secure payment ✓ Cancel anytime'
          }
        </p>
      </div>
    </section>
  )
}