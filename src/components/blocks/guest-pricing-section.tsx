"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { GUEST_PRICING_PLANS, calculateDynamicPrice, calculateSavings } from "@/lib/guest-pricing"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check, Sparkles, Zap, Crown, Star } from "lucide-react"

interface GuestPricingSectionProps {
  title: string
  subtitle: string
  userSegment?: 'new' | 'returning' | 'premium'
  isHoliday?: boolean
  onPlanSelect?: (planId: string) => void
}

export function GuestPricingSection({
  title,
  subtitle,
  userSegment = 'new',
  isHoliday = false,
  onPlanSelect
}: GuestPricingSectionProps) {
  const pathname = usePathname()
  const currentLocale = pathname.split('/')[1] || 'en'

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'single':
        return <Sparkles className="h-5 w-5" />
      case 'bundle':
        return <Zap className="h-5 w-5" />
      case 'basic':
        return <Star className="h-5 w-5" />
      case 'pro':
        return <Crown className="h-5 w-5" />
      default:
        return <Sparkles className="h-5 w-5" />
    }
  }

  const getPlanColor = (planId: string, isPopular?: boolean) => {
    if (isPopular) {
      return "bg-gradient-to-r from-purple-500 to-pink-500 text-white"
    }
    
    switch (planId) {
      case 'single':
        return "bg-blue-100 text-blue-600"
      case 'bundle':
        return "bg-green-100 text-green-600"
      case 'basic':
        return "bg-orange-100 text-orange-600"
      case 'pro':
        return "bg-purple-100 text-purple-600"
      default:
        return "bg-gray-100 text-gray-600"
    }
  }

  const handlePlanSelect = (planId: string) => {
    if (onPlanSelect) {
      onPlanSelect(planId)
    } else {
      // Default behavior - redirect to checkout
      window.location.href = `/checkout?plan=${planId}`
    }
  }

  return (
    <section className="flex flex-col items-center gap-12 py-16 px-4">
      {/* Header */}
      <div className="space-y-8 text-center max-w-3xl mx-auto">
        <div className="space-y-4">
          <h1 className="text-4xl font-bold md:text-5xl lg:text-6xl bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
            {title}
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            {subtitle}
          </p>
        </div>

        {/* Special Offers */}
        {(userSegment === 'new' || isHoliday) && (
          <div className="flex flex-wrap justify-center gap-2">
            {userSegment === 'new' && (
              <Badge className="bg-green-100 text-green-700 border-green-200">
                {currentLocale === 'zh' ? '🎉 新用户50%折扣' : '🎉 50% Off for New Users'}
              </Badge>
            )}
            {isHoliday && (
              <Badge className="bg-red-100 text-red-700 border-red-200">
                {currentLocale === 'zh' ? '🎄 节日特惠30%折扣' : '🎄 Holiday Special 30% Off'}
              </Badge>
            )}
          </div>
        )}
      </div>

      {/* Pricing Cards */}
      <div className="w-full max-w-7xl mx-auto px-4">
        <div className="grid gap-6 lg:gap-8 grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 items-stretch justify-items-center">
          {GUEST_PRICING_PLANS.map((plan, index) => {
            const dynamicPrice = calculateDynamicPrice(plan, userSegment, isHoliday)
            const savings = calculateSavings(plan)
            const isDiscounted = dynamicPrice < plan.price
            
            return (
              <div
                key={plan.id}
                className={cn(
                  "w-full max-w-sm transition-all duration-300 hover:scale-105",
                  "animate-fade-in-up"
                )}
                style={{
                  animationDelay: `${index * 100}ms`,
                  animationFillMode: 'both'
                }}
              >
                <Card className={cn(
                  "relative p-6 h-full flex flex-col",
                  plan.popular && "ring-2 ring-purple-500 shadow-lg scale-105"
                )}>
                  {/* Popular Badge */}
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1">
                        {currentLocale === 'zh' ? plan.badgeZh : plan.badge}
                      </Badge>
                    </div>
                  )}

                  <div className="text-center flex-1">
                    {/* Plan Icon */}
                    <div className={cn(
                      "w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4",
                      getPlanColor(plan.id, plan.popular)
                    )}>
                      {getPlanIcon(plan.id)}
                    </div>

                    {/* Plan Name */}
                    <h3 className="text-xl font-bold mb-2">
                      {currentLocale === 'zh' ? plan.nameZh : plan.name}
                    </h3>

                    {/* Price */}
                    <div className="mb-4">
                      <div className="flex items-center justify-center gap-2">
                        {isDiscounted && (
                          <span className="text-lg text-gray-400 line-through">
                            ${plan.price}
                          </span>
                        )}
                        <span className="text-3xl font-bold text-gray-900">
                          ${dynamicPrice}
                        </span>
                      </div>
                      {plan.credits > 0 && (
                        <p className="text-sm text-gray-500">
                          {currentLocale === 'zh' 
                            ? `${plan.credits}张照片` 
                            : `${plan.credits} photo${plan.credits > 1 ? 's' : ''}`
                          }
                        </p>
                      )}
                      {plan.credits === -1 && (
                        <p className="text-sm text-gray-500">
                          {currentLocale === 'zh' ? '无限张照片' : 'Unlimited photos'}
                        </p>
                      )}
                    </div>

                    {/* Savings */}
                    {savings > 0 && (
                      <div className="mb-4">
                        <Badge className="bg-green-100 text-green-700 border-green-200">
                          {currentLocale === 'zh' 
                            ? `节省 $${savings}` 
                            : `Save $${savings}`
                          }
                        </Badge>
                      </div>
                    )}

                    {/* Description */}
                    <p className="text-gray-600 mb-6">
                      {currentLocale === 'zh' ? plan.descriptionZh : plan.description}
                    </p>

                    {/* Features */}
                    <ul className="space-y-3 mb-8 text-left">
                      {(currentLocale === 'zh' ? plan.featuresZh : plan.features).map((feature, idx) => (
                        <li key={idx} className="flex items-start">
                          <Check className="h-4 w-4 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                          <span className="text-sm text-gray-600">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* CTA Button */}
                  <Button
                    className={cn(
                      "w-full",
                      plan.popular
                        ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'
                        : ''
                    )}
                    variant={plan.popular ? 'default' : 'outline'}
                    onClick={() => handlePlanSelect(plan.id)}
                  >
                    {currentLocale === 'zh' 
                      ? (plan.price === 0 ? '免费试用' : '立即购买')
                      : (plan.price === 0 ? 'Try Free' : 'Get Started')
                    }
                  </Button>
                </Card>
              </div>
            )
          })}
        </div>

        {/* Additional Info */}
        <div className="text-center mt-12">
          <p className="text-sm text-gray-500 mb-4">
            {currentLocale === 'zh' 
              ? '所有计划包含商用授权 • 24小时内不满意全额退款'
              : 'All plans include commercial license • 24-hour money-back guarantee'
            }
          </p>
          <div className="flex justify-center items-center gap-4 text-xs text-gray-400">
            <span>🔒 {currentLocale === 'zh' ? '安全支付' : 'Secure Payment'}</span>
            <span>⚡ {currentLocale === 'zh' ? '即时交付' : 'Instant Delivery'}</span>
            <span>🎯 {currentLocale === 'zh' ? '无隐藏费用' : 'No Hidden Fees'}</span>
          </div>
        </div>
      </div>
    </section>
  )
}
