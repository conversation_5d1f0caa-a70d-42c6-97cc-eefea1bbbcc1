'use client';

import { useState, useEffect, useCallback } from 'react';
import { useLocalStorage } from './useLocalStorage';

interface GuestSession {
  sessionId: string;
  freeCreditsUsed: number;
  maxFreeCredits: number;
  expiresAt: string;
}

interface GuestSessionHook {
  session: GuestSession | null;
  canGeneratePreview: boolean;
  isLoading: boolean;
  refreshSession: () => Promise<void>;
  createNewSession: () => Promise<void>;
}

export function useGuestSession(): GuestSessionHook {
  const [session, setSession] = useLocalStorage<GuestSession | null>('hera_guest_session', null);
  const [isLoading, setIsLoading] = useState(true);

  const createNewSession = useCallback(async () => {
    try {
      const response = await fetch('/api/guest/session', {
        method: 'POST'
      });

      const data = await response.json();

      if (data.success) {
        const newSession: GuestSession = {
          sessionId: data.sessionId,
          freeCreditsUsed: 0,
          maxFreeCredits: data.maxFreeCredits,
          expiresAt: new Date(Date.now() + data.expiresIn * 1000).toISOString()
        };
        setSession(newSession);
        console.log('✅ Guest session created:', newSession.sessionId);
      } else {
        throw new Error(data.error || 'Failed to create session');
      }
    } catch (error) {
      console.error('❌ Failed to create guest session:', error);
      throw error;
    }
  }, [setSession]);

  const refreshSession = useCallback(async () => {
    if (!session?.sessionId) return;

    try {
      const response = await fetch(`/api/guest/session?sessionId=${session.sessionId}`);
      const data = await response.json();

      if (data.success) {
        setSession(prev => prev ? {
          ...prev,
          freeCreditsUsed: data.freeCreditsUsed,
          maxFreeCredits: data.maxFreeCredits
        } : null);
      } else {
        // 会话无效或过期，创建新会话
        await createNewSession();
      }
    } catch (error) {
      console.error('❌ Failed to refresh session:', error);
      // 出错时创建新会话
      await createNewSession();
    }
  }, [session?.sessionId, setSession, createNewSession]);

  // 检查会话是否过期
  const isSessionExpired = useCallback(() => {
    if (!session?.expiresAt) return true;
    return new Date(session.expiresAt) <= new Date();
  }, [session?.expiresAt]);

  // 初始化会话
  useEffect(() => {
    const initSession = async () => {
      setIsLoading(true);

      try {
        if (!session || isSessionExpired()) {
          await createNewSession();
        } else {
          await refreshSession();
        }
      } catch (error) {
        console.error('Session initialization failed:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initSession();
  }, []); // 只在组件挂载时执行

  const canGeneratePreview = session ?
    session.freeCreditsUsed < session.maxFreeCredits && !isSessionExpired() :
    false;

  return {
    session,
    canGeneratePreview,
    isLoading,
    refreshSession,
    createNewSession
  };
}
