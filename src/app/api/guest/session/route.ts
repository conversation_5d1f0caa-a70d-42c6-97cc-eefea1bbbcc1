import { NextRequest, NextResponse } from 'next/server';
import { guestSessionManager } from '@/lib/guest-session-manager';
import { guestSessionLimiter } from '@/lib/rate-limit';

export async function POST(request: NextRequest) {
  try {
    // 应用速率限制
    const identifier = request.headers.get('x-forwarded-for') ||
                      request.headers.get('x-real-ip') ||
                      'anonymous';
    const { success } = await guestSessionLimiter.check(5, identifier);
    
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      );
    }

    const clientIP = request.headers.get('x-forwarded-for') ||
                    request.headers.get('x-real-ip') ||
                    'unknown';
    
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    const sessionId = guestSessionManager.createSession(clientIP, userAgent);
    
    return NextResponse.json({
      success: true,
      sessionId,
      maxFreeCredits: 1,
      expiresIn: 24 * 60 * 60 // 24小时（秒）
    });
  } catch (error) {
    console.error('Error creating guest session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create session' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const sessionId = request.nextUrl.searchParams.get('sessionId');
    
    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID required' },
        { status: 400 }
      );
    }
    
    const session = guestSessionManager.getSession(sessionId);
    
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Session not found or expired' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      canGenerate: guestSessionManager.canGeneratePreview(sessionId),
      freeCreditsUsed: session.freeCreditsUsed,
      maxFreeCredits: session.maxFreeCredits,
      remainingCredits: guestSessionManager.getRemainingCredits(sessionId),
      expiresAt: session.expiresAt.toISOString()
    });
  } catch (error) {
    console.error('Error checking guest session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check session' },
      { status: 500 }
    );
  }
}
