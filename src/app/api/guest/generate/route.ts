import { NextRequest, NextResponse } from 'next/server';
import { guestSessionManager } from '@/lib/guest-session-manager';
import { generatePreviewImage } from '@/lib/ai-generation';
import { imageGenerationLimiter } from '@/lib/rate-limit';

export async function POST(request: NextRequest) {
  try {
    const { sessionId, imageData, style } = await request.json();

    // 验证必要参数
    if (!sessionId || !imageData || !style) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // 应用生成速率限制
    const identifier = request.headers.get('x-forwarded-for') ||
                      request.headers.get('x-real-ip') ||
                      'anonymous';
    const { success } = await imageGenerationLimiter.check(3, identifier);

    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Generation rate limit exceeded' },
        { status: 429 }
      );
    }

    // 验证游客会话
    if (!guestSessionManager.canGeneratePreview(sessionId)) {
      return NextResponse.json(
        {
          success: false,
          error: 'No free credits available',
          remainingCredits: guestSessionManager.getRemainingCredits(sessionId)
        },
        { status: 403 }
      );
    }

    console.log(`🎨 Starting preview generation for session: ${sessionId}, style: ${style}`);

    // 生成预览图
    const result = await generatePreviewImage({
      imageData,
      style,
      sessionId
    });

    // 消费免费积分
    const creditConsumed = guestSessionManager.consumeFreeCredit(sessionId);

    if (!creditConsumed) {
      console.error('❌ Failed to consume credit after successful generation');
    }

    // 记录生成历史到数据库
    await recordPreviewGeneration({
      sessionId,
      imageUrl: result.url,
      style,
      resolution: '720p',
      hasWatermark: true,
      generationTime: result.generationTime
    });

    console.log(`✅ Preview generated successfully for session: ${sessionId}`);

    return NextResponse.json({
      success: true,
      imageUrl: result.url,
      width: result.width,
      height: result.height,
      hasWatermark: result.hasWatermark,
      remainingCredits: guestSessionManager.getRemainingCredits(sessionId),
      generationTime: result.generationTime
    });

  } catch (error) {
    console.error('❌ Preview generation failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Generation failed',
        code: 'GENERATION_ERROR'
      },
      { status: 500 }
    );
  }
}

// 记录预览生成历史
async function recordPreviewGeneration(data: {
  sessionId: string;
  imageUrl: string;
  style: string;
  resolution: string;
  hasWatermark: boolean;
  generationTime: number;
}) {
  try {
    // 这里可以记录到数据库
    console.log('📝 Recording preview generation:', {
      sessionId: data.sessionId,
      style: data.style,
      resolution: data.resolution,
      generationTime: data.generationTime
    });

    // 实际实现中可以使用Prisma保存到数据库
    // await prisma.previewGeneration.create({ data });

  } catch (error) {
    console.error('Failed to record preview generation:', error);
    // 不抛出错误，避免影响主流程
  }
}
