import { NextRequest, NextResponse } from 'next/server';
import { guestSessionManager } from '@/lib/guest-session-manager';
import { validatePhoneNumber, validateEmail } from '@/lib/validation';
import { checkoutLimiter } from '@/lib/rate-limit';

interface CheckoutRequest {
  sessionId: string;
  planId: 'single' | 'bundle' | 'basic' | 'premium';
  contactInfo: {
    phone?: string;
    email?: string;
  };
  imageData?: string;
  style?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: CheckoutRequest = await request.json();
    const { sessionId, planId, contactInfo, imageData, style } = body;

    // 应用结账速率限制
    const identifier = request.headers.get('x-forwarded-for') ||
                      request.headers.get('x-real-ip') ||
                      'anonymous';
    const { success } = await checkoutLimiter.check(5, identifier);

    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Checkout rate limit exceeded' },
        { status: 429 }
      );
    }

    // 验证必要参数
    if (!sessionId || !planId || !contactInfo) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // 验证联系方式（至少需要手机号或邮箱）
    if (!contactInfo.phone && !contactInfo.email) {
      return NextResponse.json(
        { success: false, error: 'Phone number or email required' },
        { status: 400 }
      );
    }

    if (contactInfo.phone && !validatePhoneNumber(contactInfo.phone)) {
      return NextResponse.json(
        { success: false, error: 'Invalid phone number' },
        { status: 400 }
      );
    }

    if (contactInfo.email && !validateEmail(contactInfo.email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email address' },
        { status: 400 }
      );
    }

    // 验证游客会话
    const session = guestSessionManager.getSession(sessionId);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired session' },
        { status: 404 }
      );
    }

    // 获取定价信息
    const pricing = getPricingForPlan(planId);
    if (!pricing) {
      return NextResponse.json(
        { success: false, error: 'Invalid plan' },
        { status: 400 }
      );
    }

    console.log(`💳 Creating checkout session for guest: ${sessionId}, plan: ${planId}`);

    // 创建Stripe结账会话
    const stripeSession = await createStripeCheckoutSession({
      planId,
      amount: pricing.amount,
      currency: 'usd',
      customerEmail: contactInfo.email || null,
      customerPhone: contactInfo.phone || null,
      guestSessionId: sessionId,
      metadata: {
        isGuestOrder: 'true',
        sessionId,
        planId,
        imageData: imageData || '',
        style: style || '',
        contactPhone: contactInfo.phone || '',
        contactEmail: contactInfo.email || ''
      },
      successUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancelUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/checkout/cancel`
    });

    console.log(`✅ Stripe checkout session created: ${stripeSession.id}`);

    return NextResponse.json({
      success: true,
      checkoutUrl: stripeSession.url,
      sessionId: stripeSession.id,
      amount: pricing.amount,
      currency: pricing.currency
    });

  } catch (error) {
    console.error('❌ Guest checkout failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Checkout failed',
        code: 'CHECKOUT_ERROR'
      },
      { status: 500 }
    );
  }
}

// 定价配置
function getPricingForPlan(planId: string) {
  const pricing = {
    single: { amount: 490, currency: 'usd', credits: 1 }, // $4.90
    bundle: { amount: 1290, currency: 'usd', credits: 5 }, // $12.90
    basic: { amount: 990, currency: 'usd', credits: 20 }, // $9.90/month
    premium: { amount: 1990, currency: 'usd', credits: -1 } // $19.90/month (unlimited)
  };

  return pricing[planId as keyof typeof pricing] || null;
}

// 创建Stripe结账会话的占位符函数
async function createStripeCheckoutSession(params: any) {
  // 这里应该集成实际的Stripe API
  // 暂时返回模拟数据
  return {
    id: 'cs_test_' + Math.random().toString(36).substr(2, 9),
    url: 'https://checkout.stripe.com/pay/cs_test_' + Math.random().toString(36).substr(2, 9)
  };
}
