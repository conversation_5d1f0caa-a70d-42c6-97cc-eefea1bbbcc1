import { Metadata } from 'next';
import { GuestWeddingGenerator } from '@/components/guest/GuestWeddingGenerator';
import { getMessages } from '@/i18n/routing';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale } = await params;
  const messages = await getMessages(locale);

  return {
    title: messages.guest?.title ? `${messages.guest.title} - ${messages.guest.subtitle}` : 'AI Wedding Photo Generator - Free Trial',
    description: messages.guest?.description || 'Try AI wedding photo generation for free without registration. Upload your photo, choose a style, and get professional wedding photo previews instantly.',
  };
}

export default async function GuestPage({ params }: PageProps) {
  const { locale } = await params;
  const messages = await getMessages(locale);
  const t = messages.guest || {};

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-purple-50 pt-20">
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full text-sm font-medium text-purple-700 mb-6">
            ✨ {t.subtitle || 'Free Trial - No Registration Required'}
          </div>

          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-6">
            {t.title || 'AI Wedding Photos'}
          </h1>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            {t.description || 'Experience AI wedding photo generation instantly without registration. Upload a photo, choose your favorite style, and get professional wedding photo previews in 2-3 minutes.'}
          </p>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🆓</span>
              </div>
              <h3 className="font-semibold mb-2">
                {t.features?.free?.title || 'Completely Free'}
              </h3>
              <p className="text-sm text-gray-600">
                {t.features?.free?.description || 'No registration required, free preview generation'}
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">⚡</span>
              </div>
              <h3 className="font-semibold mb-2">
                {t.features?.fast?.title || 'Lightning Fast'}
              </h3>
              <p className="text-sm text-gray-600">
                {t.features?.fast?.description || 'AI generation completed in 2-3 minutes'}
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎨</span>
              </div>
              <h3 className="font-semibold mb-2">
                {t.features?.styles?.title || 'Multiple Styles'}
              </h3>
              <p className="text-sm text-gray-600">
                {t.features?.styles?.description || '6 beautiful wedding photo styles available'}
              </p>
            </div>
          </div>
        </div>

        {/* Main Generator Component */}
        <GuestWeddingGenerator />

        {/* Upgrade CTA */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl p-8 text-white">
            <h2 className="text-2xl font-bold mb-4">
              {locale === 'zh' ? '喜欢预览效果？升级获取高清版本' : 'Love the preview? Upgrade for HD version'}
            </h2>
            <p className="text-purple-100 mb-6">
              {locale === 'zh' 
                ? '升级到高清无水印版本，享受4K专业品质，支持商用授权'
                : 'Upgrade to HD watermark-free version, enjoy 4K professional quality with commercial license'
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <div className="bg-white/20 rounded-lg p-4">
                <div className="text-2xl font-bold">$4.9</div>
                <div className="text-sm text-purple-100">
                  {locale === 'zh' ? '单张高清图' : 'Single HD Photo'}
                </div>
              </div>
              <div className="bg-white/20 rounded-lg p-4">
                <div className="text-2xl font-bold">$12.9</div>
                <div className="text-sm text-purple-100">
                  {locale === 'zh' ? '5张套餐' : '5-Photo Bundle'}
                </div>
              </div>
              <div className="bg-white/20 rounded-lg p-4">
                <div className="text-2xl font-bold">$9.9</div>
                <div className="text-sm text-purple-100">
                  {locale === 'zh' ? '月度会员' : 'Monthly Plan'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
