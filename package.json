{"name": "nextlaunchpad", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "npx dotenv-cli -e .env.local -- npx prisma db push", "db:pull": "npx dotenv-cli -e .env.local -- npx prisma db pull", "db:generate": "npx prisma generate", "db:studio": "npx dotenv-cli -e .env.local -- npx prisma studio", "db:sync": "pnpm run db:pull && pnpm run db:push && pnpm run db:generate", "db:test:push": "npx dotenv-cli -e .env.test -- npx prisma db push", "db:test:init": "node src/tests/init-test-db.js", "db:test:studio": "npx dotenv-cli -e .env.test -- npx prisma studio", "postinstall": "prisma generate", "test:db": "pnpm run db:test:init && npx dotenv-cli -e .env.test -- npx jest --config src/tests/jest.config.js", "test:db:setup": "pnpm run db:test:push && pnpm run db:generate", "test:db:docker": "docker-compose up -d && npx dotenv-cli -e .env.test -- npx prisma db push && pnpm run test:db && docker-compose down", "test:debug": "node src/tests/debug.js", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "dependencies": {"@auth/core": "^0.34.2", "@auth/prisma-adapter": "^2.9.1", "@number-flow/react": "^0.5.10", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/stripe-js": "^5.10.0", "@types/bcryptjs": "^3.0.0", "@types/google-one-tap": "^1.2.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "framer-motion": "^12.17.3", "google-auth-library": "^9.15.1", "jwt-decode": "^4.0.0", "lru-cache": "^11.1.0", "lucide-react": "^0.468.0", "next": "15.2.3", "next-auth": "^4.24.11", "next-intl": "^3.26.5", "next-themes": "^0.4.6", "openai": "^5.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "sonner": "^1.7.4", "stripe": "^17.7.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.25.71"}, "devDependencies": {"@prisma/client": "^6.9.0", "@types/jest": "^29.5.14", "@types/node": "^20.19.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "dotenv-cli": "^8.0.0", "eslint": "^8.57.1", "eslint-config-next": "15.2.3", "jest": "^29.7.0", "postcss": "^8.5.5", "prisma": "^6.9.0", "tailwindcss": "^3.4.17", "ts-jest": "^29.4.0", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.12.4"}